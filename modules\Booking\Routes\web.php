<?php

use Illuminate\Support\Facades\Route;

// Gateway webhook routes (no middleware needed for webhooks)
Route::post('/gateway/webhook/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@callbackPayment')->name('gateway.webhook');
Route::get('/gateway/webhook/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@callbackPayment')->name('gateway.webhook.get');

// Gateway confirm/cancel routes
Route::get('/gateway/confirm/{gateway}', '\\Modules\\Booking\\Controllers\\NormalCheckoutController@confirmPayment')->name('gateway.confirm');
Route::get('/gateway/cancel/{gateway}', '\\Modules\\Booking\\Controllers\\NormalCheckoutController@cancelPayment')->name('gateway.cancel');

// Booking confirmation routes (for payment gateways)
Route::get('/booking/confirm-payment/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@confirmPayment')->name('booking.confirm-payment');
Route::get('/booking/cancel-payment/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@cancelPayment')->name('booking.cancel-payment');

// Gateway info route (fallback)
Route::get('/gateway/info', function() {
    return redirect('/')->with('info', __('Payment information'));
})->name('gateway.info');

// Booking checkout routes
Route::group(['prefix' => config('booking.booking_route_prefix', 'booking')], function() {
    Route::post('/doCheckout', '\\Modules\\Booking\\Controllers\\BookingController@doCheckout')->name('booking.doCheckout');
    Route::get('/{code}/checkout', '\\Modules\\Booking\\Controllers\\BookingController@checkout')->name('booking.checkout');
    Route::get('/{code}/check-status', '\\Modules\\Booking\\Controllers\\BookingController@checkStatusCheckout')->name('booking.check-status');
    Route::get('/{code}', '\\Modules\\Booking\\Controllers\\BookingController@detail')->name('booking.detail');
    Route::get('/{code}/thankyou', '\\Modules\\Booking\\Controllers\\BookingController@thankyou')->name('booking.thankyou');
});

Route::middleware('web')->namespace('\\Modules\\Booking\\Controllers')->group(function () {
    Route::prefix('user/booking')->name('user.booking.')->middleware(['auth'])->group(function () {
        Route::get('{code}/ticket', 'TicketController@index')->name('ticket');
        Route::get('ticket/scan/{b}/{t}', 'TicketController@scan')->name('ticket.scan');
    });
});
