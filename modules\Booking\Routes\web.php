<?php

use Illuminate\Support\Facades\Route;

// Gateway webhook routes (no middleware needed for webhooks)
Route::post('/gateway/webhook/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@callbackPayment')->name('gateway.webhook');
Route::get('/gateway/webhook/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@callbackPayment')->name('gateway.webhook.get');

// Gateway confirm/cancel routes
Route::get('/gateway/confirm/{gateway}', '\\Modules\\Booking\\Controllers\\NormalCheckoutController@confirmPayment')->name('gateway.confirm');
Route::get('/gateway/cancel/{gateway}', '\\Modules\\Booking\\Controllers\\NormalCheckoutController@cancelPayment')->name('gateway.cancel');

Route::middleware('web')->namespace('\\Modules\\Booking\\Controllers')->group(function () {
    Route::prefix('user/booking')->name('user.booking.')->middleware(['auth'])->group(function () {
        Route::get('{code}/ticket', 'TicketController@index')->name('ticket');
        Route::get('ticket/scan/{b}/{t}', 'TicketController@scan')->name('ticket.scan');
    });
});
