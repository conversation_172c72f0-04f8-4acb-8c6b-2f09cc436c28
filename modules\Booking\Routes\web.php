<?php

use Illuminate\Support\Facades\Route;

// Gateway webhook routes (no middleware needed for webhooks)
Route::post('/gateway/webhook/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@callbackPayment')->name('gateway.webhook');
Route::get('/gateway/webhook/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@callbackPayment')->name('gateway.webhook.get');

// Gateway confirm/cancel routes
Route::get('/gateway/confirm/{gateway}', '\\Modules\\Booking\\Controllers\\NormalCheckoutController@confirmPayment')->name('gateway.confirm');
Route::get('/gateway/cancel/{gateway}', '\\Modules\\Booking\\Controllers\\NormalCheckoutController@cancelPayment')->name('gateway.cancel');

// Booking confirmation routes (for payment gateways)
Route::get('/booking/confirm-payment/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@confirmPayment')->name('booking.confirm-payment');
Route::get('/booking/cancel-payment/{gateway}', '\\Modules\\Booking\\Controllers\\BookingController@cancelPayment')->name('booking.cancel-payment');

// Gateway info route (fallback)
Route::get('/gateway/info', function() {
    return redirect('/')->with('info', __('Payment information'));
})->name('gateway.info');

Route::middleware('web')->namespace('\\Modules\\Booking\\Controllers')->group(function () {
    Route::prefix('user/booking')->name('user.booking.')->middleware(['auth'])->group(function () {
        Route::get('{code}/ticket', 'TicketController@index')->name('ticket');
        Route::get('ticket/scan/{b}/{t}', 'TicketController@scan')->name('ticket.scan');
    });
});
