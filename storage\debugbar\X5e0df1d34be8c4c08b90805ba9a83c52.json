{"__meta": {"id": "X5e0df1d34be8c4c08b90805ba9a83c52", "datetime": "2025-07-08 00:31:31", "utime": **********.979687, "method": "GET", "uri": "/mazar/public/admin", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751934688.968281, "end": **********.97971, "duration": 3.0114290714263916, "duration_str": "3.01s", "measures": [{"label": "Booting", "start": 1751934688.968281, "relative_start": 0, "end": **********.492193, "relative_end": **********.492193, "duration": 0.5239119529724121, "duration_str": "524ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.492236, "relative_start": 0.5239548683166504, "end": **********.979713, "relative_end": 2.86102294921875e-06, "duration": 2.4874770641326904, "duration_str": "2.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 9346144, "peak_usage_str": "9MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "1x Dashboard::index", "param_count": null, "params": [], "start": **********.949821, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Dashboard/Views/index.blade.phpDashboard::index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FDashboard%2FViews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Dashboard::index"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.098407, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x Layout::admin.app", "param_count": null, "params": [], "start": **********.125693, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/app.blade.phpLayout::admin.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.app"}, {"name": "1x Layout::admin.parts.global-script", "param_count": null, "params": [], "start": **********.362646, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/global-script.blade.phpLayout::admin.parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.global-script"}, {"name": "1x Layout::admin.parts.header", "param_count": null, "params": [], "start": **********.464479, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.phpLayout::admin.parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.header"}, {"name": "1x Layout::admin.parts.sidebar", "param_count": null, "params": [], "start": **********.701933, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.phpLayout::admin.parts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.sidebar"}, {"name": "1x Layout::admin.parts.bc", "param_count": null, "params": [], "start": **********.706812, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/bc.blade.phpLayout::admin.parts.bc", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fbc.blade.php&line=1", "ajax": false, "filename": "bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.bc"}, {"name": "1x Media::browser", "param_count": null, "params": [], "start": **********.79214, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Media/Views/browser.blade.phpMedia::browser", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FViews%2Fbrowser.blade.php&line=1", "ajax": false, "filename": "browser.blade.php", "line": "?"}, "render_count": 1, "name_original": "Media::browser"}, {"name": "1x Pro::admin.upgrade-modal", "param_count": null, "params": [], "start": **********.882601, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\app\\Pro/Views/admin/upgrade-modal.blade.phpPro::admin.upgrade-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FPro%2FViews%2Fadmin%2Fupgrade-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Pro::admin.upgrade-modal"}, {"name": "1x Ai::frontend.text-generate", "param_count": null, "params": [], "start": **********.914518, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules\\Ai/Views/frontend/text-generate.blade.phpAi::frontend.text-generate", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FAi%2FViews%2Ffrontend%2Ftext-generate.blade.php&line=1", "ajax": false, "filename": "text-generate.blade.php", "line": "?"}, "render_count": 1, "name_original": "Ai::frontend.text-generate"}]}, "route": {"uri": "GET admin", "middleware": "web, dashboard", "controller": "Modules\\Dashboard\\Admin\\DashboardController@index", "namespace": "Modules\\Dashboard\\Admin", "prefix": "admin", "where": [], "as": "admin.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FDashboard%2FAdmin%2FDashboardController.php&line=11\" onclick=\"\">modules/Dashboard/Admin/DashboardController.php:11-20</a>"}, "queries": {"nb_statements": 105, "nb_failed_statements": 0, "accumulated_duration": 0.09805000000000001, "accumulated_duration_str": "98.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `core_settings` where `name` = 'update_to_110' limit 1", "type": "query", "params": [], "bindings": ["update_to_110"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.500992, "duration": 0.006860000000000001, "duration_str": "6.86ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_120' limit 1", "type": "query", "params": [], "bindings": ["update_to_120"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.515131, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_130' limit 1", "type": "query", "params": [], "bindings": ["update_to_130"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.5238261, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_140' limit 1", "type": "query", "params": [], "bindings": ["update_to_140"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.530687, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_150' limit 1", "type": "query", "params": [], "bindings": ["update_to_150"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.537766, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_151' limit 1", "type": "query", "params": [], "bindings": ["update_to_151"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.54485, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_160' limit 1", "type": "query", "params": [], "bindings": ["update_to_160"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.551615, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_170' limit 1", "type": "query", "params": [], "bindings": ["update_to_170"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.558474, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_182' limit 1", "type": "query", "params": [], "bindings": ["update_to_182"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.5649621, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_190' limit 1", "type": "query", "params": [], "bindings": ["update_to_190"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.571873, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_200' limit 1", "type": "query", "params": [], "bindings": ["update_to_200"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.578928, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_210' limit 1", "type": "query", "params": [], "bindings": ["update_to_210"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.586315, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_220' limit 1", "type": "query", "params": [], "bindings": ["update_to_220"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.593557, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_230' limit 1", "type": "query", "params": [], "bindings": ["update_to_230"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.601067, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_240' limit 1", "type": "query", "params": [], "bindings": ["update_to_240"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.610214, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_250' limit 1", "type": "query", "params": [], "bindings": ["update_to_250"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.6216562, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_300' limit 1", "type": "query", "params": [], "bindings": ["update_to_300"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.6308799, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_310' limit 1", "type": "query", "params": [], "bindings": ["update_to_310"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.638492, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_340' limit 1", "type": "query", "params": [], "bindings": ["update_to_340"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.6467168, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_350' limit 1", "type": "query", "params": [], "bindings": ["update_to_350"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.6549082, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_360' limit 1", "type": "query", "params": [], "bindings": ["update_to_360"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.66228, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.702127, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.709681, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'system_log_view')", "type": "query", "params": [], "bindings": ["1", "system_log_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.715565, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'dashboard_access')", "type": "query", "params": [], "bindings": ["1", "dashboard_access"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.7278728, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_bookings` where `status` != 'draft' and `bravo_bookings`.`deleted_at` is null order by `id` desc limit 10", "type": "query", "params": [], "bindings": ["draft"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>!=</code> operator is not standard. Use the <code>&lt;&gt;</code> operator to test for inequality instead."], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 298}, {"index": 16, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.734062, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Booking.php:298", "source": "modules/Booking/Models/Booking.php:298", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=298", "ajax": false, "filename": "Booking.php", "line": "298"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price , sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning  from `bravo_bookings` where `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 305}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.740041, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Booking.php:305", "source": "modules/Booking/Models/Booking.php:305", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=305", "ajax": false, "filename": "Booking.php", "line": "305"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_bookings` where `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["draft", "cancelled", "unpaid"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 306}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7444139, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Booking.php:306", "source": "modules/Booking/Models/Booking.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=306", "ajax": false, "filename": "Booking.php", "line": "306"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_disable' limit 1", "type": "query", "params": [], "bindings": ["car_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.7514231, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'flight_disable' limit 1", "type": "query", "params": [], "bindings": ["flight_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.761113, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'boat_disable' limit 1", "type": "query", "params": [], "bindings": ["boat_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.770148, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'coupon_disable' limit 1", "type": "query", "params": [], "bindings": ["coupon_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.780999, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'location_disable' limit 1", "type": "query", "params": [], "bindings": ["location_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.789987, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'popup_disable' limit 1", "type": "query", "params": [], "bindings": ["popup_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.797815, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_hotels` where `status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.810356, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_spaces` where `status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.814959, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_cars` where `status` = 'publish' and `bravo_cars`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.81937, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_events` where `status` = 'publish' and `bravo_events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.824479, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_tours` where `status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.828517, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_main' limit 1", "type": "query", "params": [], "bindings": ["currency_main"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.835169, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_format' limit 1", "type": "query", "params": [], "bindings": ["currency_format"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.851805, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_thousand' limit 1", "type": "query", "params": [], "bindings": ["currency_thousand"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.8604589, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_no_decimal' limit 1", "type": "query", "params": [], "bindings": ["currency_no_decimal"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.8689442, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_decimal' limit 1", "type": "query", "params": [], "bindings": ["currency_decimal"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.878704, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'extra_currency' limit 1", "type": "query", "params": [], "bindings": ["extra_currency"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.88816, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 00:00:00' and '2025-07-07 23:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 00:00:00", "2025-07-07 23:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 430}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.926097, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Booking.php:430", "source": "modules/Booking/Models/Booking.php:430", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=430", "ajax": false, "filename": "Booking.php", "line": "430"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'date_format' limit 1", "type": "query", "params": [], "bindings": ["date_format"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.932387, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-08 00:00:00' and '2025-07-08 23:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-08 00:00:00", "2025-07-08 23:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 430}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.938805, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Booking.php:430", "source": "modules/Booking/Models/Booking.php:430", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=430", "ajax": false, "filename": "Booking.php", "line": "430"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-09 00:00:00' and '2025-07-09 23:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-09 00:00:00", "2025-07-09 23:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 430}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.94435, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Booking.php:430", "source": "modules/Booking/Models/Booking.php:430", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=430", "ajax": false, "filename": "Booking.php", "line": "430"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_favicon' limit 1", "type": "query", "params": [], "bindings": ["site_favicon"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.341614, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '228' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["228"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": "view", "name": "Layout::admin.app", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/app.blade.php", "line": 17}], "start": **********.351095, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'map_provider' limit 1", "type": "query", "params": [], "bindings": ["map_provider"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.426521, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'map_gmap_key' limit 1", "type": "query", "params": [], "bindings": ["map_gmap_key"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.435777, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_first_day_of_the_weekin_calendar' limit 1", "type": "query", "params": [], "bindings": ["site_first_day_of_the_weekin_calendar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.448112, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1213}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.675087, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1213", "source": "app/Helpers/AppHelper.php:1213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1213", "ajax": false, "filename": "AppHelper.php", "line": "1213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) and `read_at` is null limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1214}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.6803858, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1214", "source": "app/Helpers/AppHelper.php:1214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1214", "ajax": false, "filename": "AppHelper.php", "line": "1214"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6868498, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 132}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.693249, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_news` where `status` = 'pending' and `core_news`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/News/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\ModuleProvider.php", "line": 31}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 238}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 249}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9617538, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:31", "source": "modules/News/ModuleProvider.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModuleProvider.php&line=31", "ajax": false, "filename": "ModuleProvider.php", "line": "31"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `users` where `verify_submit_status` in ('new', 'partial') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["new", "partial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 407}, {"index": 17, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 47}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 238}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 249}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.967098, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "User.php:407", "source": "app/User.php:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=407", "ajax": false, "filename": "User.php", "line": "407"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `user_upgrade_request` where `status` = 'pending' and exists (select * from `users` where `user_upgrade_request`.`user_id` = `users`.`id` and `users`.`deleted_at` is null) and `user_upgrade_request`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 48}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 238}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 249}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9720912, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:48", "source": "modules/User/ModuleProvider.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=48", "ajax": false, "filename": "ModuleProvider.php", "line": "48"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'user_disable_verification_feature' limit 1", "type": "query", "params": [], "bindings": ["user_disable_verification_feature"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.982403, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `bravo_booking_payments` where `object_model` = 'plan' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["plan", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 93}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 238}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 249}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9903312, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:93", "source": "modules/User/ModuleProvider.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=93", "ajax": false, "filename": "ModuleProvider.php", "line": "93"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_booking_payments` where `object_model` = 'wallet_deposit' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["wallet_deposit", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Wallet/DepositPayment.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Wallet\\DepositPayment.php", "line": 15}, {"index": 17, "namespace": null, "name": "modules/Report/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Report\\ModuleProvider.php", "line": 18}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 238}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 249}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.9982, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "DepositPayment.php:15", "source": "modules/User/Models/Wallet/DepositPayment.php:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FWallet%2FDepositPayment.php&line=15", "ajax": false, "filename": "DepositPayment.php", "line": "15"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_payouts` where `status` = 'initial'", "type": "query", "params": [], "bindings": ["initial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Vendor/Models/VendorPayout.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\Models\\VendorPayout.php", "line": 63}, {"index": 17, "namespace": null, "name": "modules/Vendor/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\ModuleProvider.php", "line": 27}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 238}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 249}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.007781, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "VendorPayout.php:63", "source": "modules/Vendor/Models/VendorPayout.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FVendor%2FModels%2FVendorPayout.php&line=63", "ajax": false, "filename": "VendorPayout.php", "line": "63"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'setting_update')", "type": "query", "params": [], "bindings": ["1", "setting_update"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.026303, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'menu_view')", "type": "query", "params": [], "bindings": ["1", "menu_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.034686, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'language_manage')", "type": "query", "params": [], "bindings": ["1", "language_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.04583, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'language_translation')", "type": "query", "params": [], "bindings": ["1", "language_translation"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.055147, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_view')", "type": "query", "params": [], "bindings": ["1", "hotel_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.066889, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_create')", "type": "query", "params": [], "bindings": ["1", "hotel_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.079168, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "hotel_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.0891209, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_view')", "type": "query", "params": [], "bindings": ["1", "space_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.099952, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_create')", "type": "query", "params": [], "bindings": ["1", "space_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.112155, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "space_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.121628, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_view')", "type": "query", "params": [], "bindings": ["1", "car_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.132616, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_create')", "type": "query", "params": [], "bindings": ["1", "car_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.144715, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "car_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.154141, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_view')", "type": "query", "params": [], "bindings": ["1", "event_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.165025, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_create')", "type": "query", "params": [], "bindings": ["1", "event_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.175524, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "event_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.183809, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_view')", "type": "query", "params": [], "bindings": ["1", "tour_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.195636, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_create')", "type": "query", "params": [], "bindings": ["1", "tour_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.206446, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_manage_others')", "type": "query", "params": [], "bindings": ["1", "tour_manage_others"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.214703, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "tour_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.224758, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'media_upload')", "type": "query", "params": [], "bindings": ["1", "media_upload"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.2344449, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_view')", "type": "query", "params": [], "bindings": ["1", "news_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.245375, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_create')", "type": "query", "params": [], "bindings": ["1", "news_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.258846, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'page_view')", "type": "query", "params": [], "bindings": ["1", "page_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.268456, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'user_view')", "type": "query", "params": [], "bindings": ["1", "user_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.2788289, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'role_view')", "type": "query", "params": [], "bindings": ["1", "role_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.290307, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'newsletter_manage')", "type": "query", "params": [], "bindings": ["1", "newsletter_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.298919, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'report_view')", "type": "query", "params": [], "bindings": ["1", "report_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.312151, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'contact_manage')", "type": "query", "params": [], "bindings": ["1", "contact_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.324418, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'user_create')", "type": "query", "params": [], "bindings": ["1", "user_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.332854, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'coupon_view')", "type": "query", "params": [], "bindings": ["1", "coupon_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.3439672, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'location_view')", "type": "query", "params": [], "bindings": ["1", "location_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.3537211, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'review_manage_others')", "type": "query", "params": [], "bindings": ["1", "review_manage_others"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.365377, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'popup_view')", "type": "query", "params": [], "bindings": ["1", "popup_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.3773901, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'ai_text_generate')", "type": "query", "params": [], "bindings": ["1", "ai_text_generate"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.3892002, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_view')", "type": "query", "params": [], "bindings": ["1", "support_ticket_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.403246, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_manage')", "type": "query", "params": [], "bindings": ["1", "support_ticket_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.415195, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_view')", "type": "query", "params": [], "bindings": ["1", "support_topic_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.427339, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_create')", "type": "query", "params": [], "bindings": ["1", "support_topic_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.441144, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'theme_manage')", "type": "query", "params": [], "bindings": ["1", "theme_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.45587, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Core\\Models\\Settings": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Modules\\Booking\\Models\\Booking": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=1", "ajax": false, "filename": "Booking.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Modules\\Media\\Models\\MediaFile": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FModels%2FMediaFile.php&line=1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}}, "count": 41, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "bc_current_currency": "tnd", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin", "status_code": "<pre class=sf-dump id=sf-dump-1796790446 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1796790446\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1628725665 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1628725665\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1985238587 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1985238587\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/mazar/public/pro/upgrade</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6ImN4c2wzK0dPUCtBSWNTeHBVQlNZN0E9PSIsInZhbHVlIjoickNxelBRbWpVMFgwc0tkVHk4M3RBMXRaNEgwZUdhTTJ3dkJkY29ZUmpKN0FlOU53U3l1d05mSHdnZ1JtcDVQSDV4b0ZzK09vclVSWmpzdlN6QlUyOTBXRWVwaHpZVmp5eC9XR1hndmJKWGw1cGZTK0RjeFBiaUlNT3NuRGoybjEiLCJtYWMiOiJhOTVjOGVhMDA2M2Y1MTJmNzg2M2M4NzRhNzQzZjNlNzE3YjcyMzE5MjVjMWI0MzE1MDkzMGZlNGJiNTMyYjI2IiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IkNyeVVaZ3FVSjR1OHgwUjlxU0F0cHc9PSIsInZhbHVlIjoiOEVTejBMbSszWDlycE9NajdvMmVhYjZiTk8wS3dPc2ZVbjVDak1CSnNuN1BRTklMMGdlbHJZR3U4ZmQ3aklqcXZxdHhkOUtkVEhjaVlGanhzT2xrQWxFc2ZKZTZLZk94dCtTdG1IM3YzY082MEkvcERzRHZoTTF5NHY0UjBOSlIiLCJtYWMiOiI0ODlkZGJmM2Q4Njk2MTZhOTBmNGJmNmI1MmYzMDU4ZWQxOTBmOTQ4NmY1NmI2MzdkZGRjODg2NmZlOTUzOTllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-31384308 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EL5tyKZMFAXgFoH4oVl3aDpHOSef0nw7JBkJdvjn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31384308\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 00:31:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjBUdS9WL3BMcUx3RVgvTFhkREZhU3c9PSIsInZhbHVlIjoiZWtTZzFqUUh1ejlUL1lIZHNsQm0vK1FQWmJ6S2xqVkF5UUx0SVJRdktuMDZGdGo4OGtSVitHeUFvWkRhekoweGtFS3FGR3JUT2lKTWJsNEVnOVZKMTZ5dDdnR0wyeE4vZXFnNjFXTVVIRUYxQzlOZXM1bGJ3cDdmWVVibkNZNVUiLCJtYWMiOiJkMGRiZDc5NTc5NjgwOWQzMWNhODA3ZTY3M2VjYzM4ZTBlNzVmNjdiZTFjNTYzZWRmMjkxNTBjYTA3NWUwMzhhIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 02:31:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6Ino2M3J6Y28ra2lxSVpUNlVYaXFjeHc9PSIsInZhbHVlIjoiakVVSmU2bUNSYkFIRXNUaVNEZUh0N0thTVhxWDE3RUNwdE5PM21vZkVLaXVXU0RpM3lqbWc2YVNqbVFYeldKaU4vV1NpcVZ0b2tnUlpSVFByeCtmbHY3bXArNWtzM2F6dFN2QUhySkJKNEg5Y1MvSCtIVTl4L3RLM2crTzYvRDYiLCJtYWMiOiJjZjRlNWM2YWVlYjg5MDEwMGM1MzA3NzEyMGQxNWM4NzUzMjM2NmRlNWI0NThjY2RmNTFhZWVkYzMwMzMwZGFmIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 02:31:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjBUdS9WL3BMcUx3RVgvTFhkREZhU3c9PSIsInZhbHVlIjoiZWtTZzFqUUh1ejlUL1lIZHNsQm0vK1FQWmJ6S2xqVkF5UUx0SVJRdktuMDZGdGo4OGtSVitHeUFvWkRhekoweGtFS3FGR3JUT2lKTWJsNEVnOVZKMTZ5dDdnR0wyeE4vZXFnNjFXTVVIRUYxQzlOZXM1bGJ3cDdmWVVibkNZNVUiLCJtYWMiOiJkMGRiZDc5NTc5NjgwOWQzMWNhODA3ZTY3M2VjYzM4ZTBlNzVmNjdiZTFjNTYzZWRmMjkxNTBjYTA3NWUwMzhhIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 02:31:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6Ino2M3J6Y28ra2lxSVpUNlVYaXFjeHc9PSIsInZhbHVlIjoiakVVSmU2bUNSYkFIRXNUaVNEZUh0N0thTVhxWDE3RUNwdE5PM21vZkVLaXVXU0RpM3lqbWc2YVNqbVFYeldKaU4vV1NpcVZ0b2tnUlpSVFByeCtmbHY3bXArNWtzM2F6dFN2QUhySkJKNEg5Y1MvSCtIVTl4L3RLM2crTzYvRDYiLCJtYWMiOiJjZjRlNWM2YWVlYjg5MDEwMGM1MzA3NzEyMGQxNWM4NzUzMjM2NmRlNWI0NThjY2RmNTFhZWVkYzMwMzMwZGFmIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 02:31:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1970594727 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/mazar/public/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">tnd</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970594727\", {\"maxDepth\":0})</script>\n"}}