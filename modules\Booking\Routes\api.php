<?php

use Illuminate\Http\Request;
use \Illuminate\Support\Facades\Route;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Booking API routes
Route::group(['prefix' => config('booking.booking_route_prefix', 'booking')], function() {
    Route::post('/addToCart', 'BookingController@addToCart')->name("api.booking.add_to_cart");
    Route::post('/addEnquiry', 'BookingController@addEnquiry')->name("api.booking.add_enquiry");
    Route::post('/doCheckout', 'BookingController@doCheckout')->name('api.booking.doCheckout');
    Route::get('/confirm/{gateway}', 'BookingController@confirmPayment');
    Route::get('/cancel/{gateway}', 'BookingController@cancelPayment');
    Route::get('/{code}', 'BookingController@detail');
    Route::get('/{code}/thankyou', 'BookingController@thankyou')->name('booking.thankyou');
    Route::get('/{code}/checkout', 'BookingController@checkout');
    Route::get('/{code}/check-status', 'BookingController@checkStatusCheckout');
});

// Gateways
Route::get('/gateways', 'BookingController@getGatewaysForApi');
