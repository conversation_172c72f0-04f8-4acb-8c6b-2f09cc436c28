<?php

namespace Modules\Ai;

use <PERSON><PERSON><PERSON>\ModuleServiceProvider;
use Mo<PERSON>les\User\Helpers\PermissionHelper;
use Modules\Ai\Drivers\AiDriver;
use Modules\Ai\Drivers\OpenAi;
use Modules\Ai\Models\Ai;

class ModuleProvider extends ModuleServiceProvider
{

    public function boot()
    {
        if (is_installed() and Ai::isEnable()) {
            $this->loadViewsFrom(__DIR__ . '/Views', 'Ai');

            $this->mergeConfigFrom(__DIR__ . '/Configs/config.php', 'ai');

            $this->loadRoutesFrom(__DIR__ . '/Routes/web.php');

            add_action('ADMIN_JS_STACK', [$this, '__addJs']);

            $this->app->singleton(AiDriver::class, function () {
                $settings = config('ai.providers');
                $default = config('ai.default');
                if (empty($settings[$default])) {
                    throw new \Exception("AI Driver not found");
                }
                return new OpenAi($settings[$default]);
            });
        }

        PermissionHelper::add([
            "ai_text_generate"
        ]);
    }

    public function register()
    {
        $this->app->register(RouterServiceProvider::class);
        parent::register();
    }

    public function __addJs()
    {
        echo view("Ai::frontend.text-generate");
    }

    public static function getAdminMenu()
    {
        if (!Ai::isEnable()) return [];

        return [
            'ai' => [
                'title' => __("AI Tools"),
                'url' => '#',
                "permission" => "ai_text_generate",
                "position" => 80,
                'icon' => "fa fa-robot",
                "group" => "system",
            ]
        ];
    }

    public static function getName()
    {
        return 'AI';
    }

    public static function getDesc()
    {
        return __('AI text generation and tools');
    }

    public static function getVersion()
    {
        return '1.0';
    }

    public static function getAuthor()
    {
        return 'MAZAR Travel';
    }
}
