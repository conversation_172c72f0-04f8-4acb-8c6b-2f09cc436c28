{"__meta": {"id": "X010ee6b2fa0515d2b299e97d4bef8716", "datetime": "2025-07-08 21:11:24", "utime": 1752009084.828383, "method": "GET", "uri": "/mazar/public/admin/module/support/ticket/category", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752009082.950069, "end": 1752009084.828486, "duration": 1.8784170150756836, "duration_str": "1.88s", "measures": [{"label": "Booting", "start": 1752009082.950069, "relative_start": 0, "end": 1752009084.523969, "relative_end": 1752009084.523969, "duration": 1.5738999843597412, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752009084.524018, "relative_start": 1.5739490985870361, "end": 1752009084.828496, "relative_end": 1.0013580322265625e-05, "duration": 0.3044779300689697, "duration_str": "304ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5283408, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/module/support/ticket/category", "middleware": "web, dashboard", "controller": "Modules\\Support\\Admin\\Ticket\\CategoryController@index", "as": "support.admin.ticket.category.index", "namespace": "Modules\\Support\\Admin", "prefix": "admin/module/support/ticket", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSupport%2FAdmin%2FTicket%2FCategoryController.php&line=22\" onclick=\"\">modules/Support/Admin/Ticket/CategoryController.php:22-51</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7uYSlISNsSRfjeAFevSm6ZfDea8g8cN8R5PYGOJ5", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public/admin/module/support/ticket/category\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/module/support/ticket/category", "status_code": "<pre class=sf-dump id=sf-dump-1346710482 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1346710482\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1081153487 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1081153487\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-711539270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-711539270\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1123013246 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">http://localhost/mazar/public/admin/module/support/ticket</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123013246\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1037306406 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1037306406\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1397295854 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 21:11:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"109 characters\">http://localhost/mazar/public/login?redirect=%2Fmazar%2Fpublic%2Fadmin%2Fmodule%2Fsupport%2Fticket%2Fcategory</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBURHg5WC82MThzSmZjTkExUExJTmc9PSIsInZhbHVlIjoiZVM5YUJ0SDB1dGxLcGNHYm4ycU1reEdDdFZ0enNKeEhla2QyM0oxd3B1azArTUZ5V2lUcTYrOVJYREphOHBwa0JCdFowTnhBZEt1Yzl5VGFCblpUWVMwTnJBNjNHWXRiWjJWa0JGMFFTQ3lIbjFhYStEK0tmb0F5OGg0QmZUQU4iLCJtYWMiOiIwOGUxODg3ODg4ZjFiMDBiNGIxNTcxZDI3N2QyNjdmYmNiYTJlZWNjMmQwMDhmZGMxY2MyMTAzODgyYmY1MTBkIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 23:11:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6Ik5RdFdiRG52SnV6MzYvZUpTQnFFbHc9PSIsInZhbHVlIjoibFd3RVZHN0pPV01RYTdvVjhHckUrSzA4SjRtbng5NndKc3hCL2ZSWEpFc0VxYmNMUTVEZDZXYzRucDI0dlpqNmhiNzZGMjIvUFhTV0I1bHgrdnA4cWhTUm1ZVW8xYzRYeEdjUTd1NVhXOS9HNWpXbDhxeTlyUE5tV1czNDBkU2kiLCJtYWMiOiJmZDhkNDA0NjVkMDk3ZDM1MTQ2YjIxMTlhMDgxOGVjZmM2ODYxOTI1NGQzMDU5YmY0ZjAwMjUzZjFiYzFjYzRkIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 23:11:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBURHg5WC82MThzSmZjTkExUExJTmc9PSIsInZhbHVlIjoiZVM5YUJ0SDB1dGxLcGNHYm4ycU1reEdDdFZ0enNKeEhla2QyM0oxd3B1azArTUZ5V2lUcTYrOVJYREphOHBwa0JCdFowTnhBZEt1Yzl5VGFCblpUWVMwTnJBNjNHWXRiWjJWa0JGMFFTQ3lIbjFhYStEK0tmb0F5OGg0QmZUQU4iLCJtYWMiOiIwOGUxODg3ODg4ZjFiMDBiNGIxNTcxZDI3N2QyNjdmYmNiYTJlZWNjMmQwMDhmZGMxY2MyMTAzODgyYmY1MTBkIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 23:11:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6Ik5RdFdiRG52SnV6MzYvZUpTQnFFbHc9PSIsInZhbHVlIjoibFd3RVZHN0pPV01RYTdvVjhHckUrSzA4SjRtbng5NndKc3hCL2ZSWEpFc0VxYmNMUTVEZDZXYzRucDI0dlpqNmhiNzZGMjIvUFhTV0I1bHgrdnA4cWhTUm1ZVW8xYzRYeEdjUTd1NVhXOS9HNWpXbDhxeTlyUE5tV1czNDBkU2kiLCJtYWMiOiJmZDhkNDA0NjVkMDk3ZDM1MTQ2YjIxMTlhMDgxOGVjZmM2ODYxOTI1NGQzMDU5YmY0ZjAwMjUzZjFiYzFjYzRkIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 23:11:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397295854\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1608758291 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7uYSlISNsSRfjeAFevSm6ZfDea8g8cN8R5PYGOJ5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">http://localhost/mazar/public/admin/module/support/ticket/category</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608758291\", {\"maxDepth\":0})</script>\n"}}