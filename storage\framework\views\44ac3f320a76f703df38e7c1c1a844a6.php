
<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="d-flex justify-content-between mb20">
            <h1 class="title-bar"><?php echo e(__("All Modules")); ?></h1>
        </div>
        <?php echo $__env->make('admin.message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="filter-div d-flex justify-content-between ">
            <div class="col-left">
                <?php if(!empty($rows)): ?>
                    <form method="post" action="<?php echo e(route('core.admin.module.bulkEdit')); ?>" class="filter-form filter-form-left d-flex justify-content-start">
                        <?php echo e(csrf_field()); ?>

                        <select name="action" class="form-control">
                            <option value=""><?php echo e(__(" Bulk Actions ")); ?></option>
                            <option value="enable"><?php echo e(__("Enable")); ?></option>
                            <option value="disable"><?php echo e(__("Disable")); ?></option>
                        </select>
                        <button class="btn-info btn btn-icon dungdt-apply-form-btn" type="button"><?php echo e(__('Apply')); ?></button>
                    </form>
                <?php endif; ?>
            </div>
            
        </div>
        <div class="panel">
            <div class="panel-body">
                <form action="" class="bravo-form-item">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                            <tr>
                                <th width="60px"><input type="checkbox" class="check-all"></th>
                                <th width="200px"> <?php echo e(__('Module name')); ?></th>
                                <th > <?php echo e(__('Description')); ?></th>
                                <th width="130px"> <?php echo e(__('Author')); ?></th>
                                <th width="100px"> <?php echo e(__('Version')); ?></th>
                                <th width="100px"> <?php echo e(__('Status')); ?></th>
                                <th width="100px"> <?php echo e(__('Actions')); ?></th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php if(!empty($rows)): ?>
                                <?php $__currentLoopData = $rows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id=>$row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="">
                                        <td><input type="checkbox" name="ids[]" class="check-item" value="<?php echo e($id); ?>">
                                        </td>
                                        <td class="title">
                                            <div class="d-flex align-items-center">
                                                <?php if($thumb = $row::getThumb()): ?>
                                                    <div class="mr-3">
                                                        <img src="<?php echo e($thumb); ?>" width="50" height="50" alt="">
                                                    </div>
                                                <?php endif; ?>
                                                <a href="#"><?php echo e($row::getName()); ?></a>
                                            </div>
                                        </td>
                                        <td>
                                            <?php echo e($row::getDesc()); ?>

                                        </td>
                                        <td>
                                            <?php echo e($row::getAuthor()); ?>

                                        </td>
                                        <td>
                                            <?php echo e($row::getVersion()); ?>

                                        </td>
                                        <td>
                                            <?php
                                                $isDisabled = setting_item(strtolower($id) . '_disable', false);
                                                $isEnabled = !$isDisabled;
                                            ?>
                                            <?php if($isEnabled): ?>
                                                <span class="badge badge-success"><?php echo e(__("Enabled")); ?></span>
                                            <?php else: ?>
                                                <span class="badge badge-secondary"><?php echo e(__("Disabled")); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-toggle="dropdown">
                                                    <?php echo e(__("Actions")); ?>

                                                </button>
                                                <div class="dropdown-menu">
                                                    <?php
                                                        $isDisabled = setting_item(strtolower($id) . '_disable', false);
                                                        $isEnabled = !$isDisabled;
                                                    ?>
                                                    <?php if($isEnabled): ?>
                                                        <a class="dropdown-item module-toggle" href="#" data-module="<?php echo e($id); ?>" data-action="disable">
                                                            <i class="fa fa-pause"></i> <?php echo e(__("Disable")); ?>

                                                        </a>
                                                    <?php else: ?>
                                                        <a class="dropdown-item module-toggle" href="#" data-module="<?php echo e($id); ?>" data-action="enable">
                                                            <i class="fa fa-play"></i> <?php echo e(__("Enable")); ?>

                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7"><?php echo e(__("No Module found")); ?></td>
                                </tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script.body'); ?>
<script>
$(document).ready(function() {
    // Handle module toggle
    $('.module-toggle').on('click', function(e) {
        e.preventDefault();

        var $this = $(this);
        var moduleId = $this.data('module');
        var action = $this.data('action');
        var $row = $this.closest('tr');

        // Show loading state
        $this.html('<i class="fa fa-spinner fa-spin"></i> ' + (action === 'enable' ? '<?php echo e(__("Enabling...")); ?>' : '<?php echo e(__("Disabling...")); ?>'));
        $this.addClass('disabled');

        $.ajax({
            url: '<?php echo e(route("core.admin.module.toggle", ":moduleId")); ?>'.replace(':moduleId', moduleId),
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Update status badge
                    var $statusCell = $row.find('td:nth-child(6)');
                    if (response.status) {
                        $statusCell.html('<span class="badge badge-success"><?php echo e(__("Enabled")); ?></span>');
                    } else {
                        $statusCell.html('<span class="badge badge-secondary"><?php echo e(__("Disabled")); ?></span>');
                    }

                    // Update action button
                    var $actionsCell = $row.find('td:nth-child(7)');
                    if (response.status) {
                        $actionsCell.html(`
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-toggle="dropdown">
                                    <?php echo e(__("Actions")); ?>

                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item module-toggle" href="#" data-module="${moduleId}" data-action="disable">
                                        <i class="fa fa-pause"></i> <?php echo e(__("Disable")); ?>

                                    </a>
                                </div>
                            </div>
                        `);
                    } else {
                        $actionsCell.html(`
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-toggle="dropdown">
                                    <?php echo e(__("Actions")); ?>

                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item module-toggle" href="#" data-module="${moduleId}" data-action="enable">
                                        <i class="fa fa-play"></i> <?php echo e(__("Enable")); ?>

                                    </a>
                                </div>
                            </div>
                        `);
                    }

                    // Re-bind event handlers for new buttons
                    $actionsCell.find('.module-toggle').on('click', arguments.callee);

                    // Show success message
                    showMessage('success', response.message);
                } else {
                    showMessage('error', response.message || '<?php echo e(__("An error occurred")); ?>');
                }
            },
            error: function(xhr) {
                var message = '<?php echo e(__("An error occurred")); ?>';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    message = xhr.responseJSON.error;
                }
                showMessage('error', message);
            },
            complete: function() {
                // Reset button state
                $this.removeClass('disabled');
            }
        });
    });

    function showMessage(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // Remove existing alerts
        $('.container-fluid .alert').remove();

        // Add new alert after the title bar
        $('.title-bar').parent().after(alertHtml);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\wamp64\www\mazar\modules/Core/Views/admin/module/index.blade.php ENDPATH**/ ?>