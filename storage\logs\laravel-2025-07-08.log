[2025-07-08 00:04:09] local.ERROR: Route [booking.confirm-payment] not defined. {"view":{"view":"C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\parts\\sidebar.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1472390916 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1776</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1472390916\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","recent_bookings":"<pre class=sf-dump id=sf-dump-1306312999 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2816</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1306312999\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","top_cards":"<pre class=sf-dump id=sf-dump-1986967149 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total revenue</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"6 characters\">purple</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-cart</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Earning</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pink</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-gift</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bookings</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Total bookings</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"22 characters\">icon ion-ios-pricetags</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1986967149\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","earning_chart_data":"<pre class=sf-dump id=sf-dump-1220638744 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>labels</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">07/07/2025</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">07/08/2025</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">07/09/2025</span>\"
  </samp>]
  \"<span class=sf-dump-key>datasets</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Revenue</span>\"
      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>backgroundColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#8892d6</span>\"
      \"<span class=sf-dump-key>stack</span>\" => \"<span class=sf-dump-str title=\"11 characters\">group-total</span>\"
    </samp>]
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>backgroundColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#F06292</span>\"
      \"<span class=sf-dump-key>stack</span>\" => \"<span class=sf-dump-str title=\"11 characters\">group-extra</span>\"
    </samp>]
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1220638744\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-291309620 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total revenue</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"6 characters\">purple</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-cart</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Earning</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pink</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-gift</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bookings</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Total bookings</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"22 characters\">icon ion-ios-pricetags</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-291309620\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","card":"<pre class=sf-dump id=sf-dump-1366966064 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
  \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
  \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
  \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
  \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1366966064\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1043450893 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1043450893\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","favicon":"<pre class=sf-dump id=sf-dump-626447387 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">10</span>\"
</pre><script>Sfdump(\"sf-dump-626447387\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","file":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Media\\Models\\MediaFile</span> {<a class=sf-dump-ref>#2878</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">media_files</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">favicon</span>\"
    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">demo/general/favicon.png</span>\"
    \"<span class=sf-dump-key>file_size</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"
    \"<span class=sf-dump-key>file_extension</span>\" => \"<span class=sf-dump-str title=\"3 characters\">png</span>\"
    \"<span class=sf-dump-key>driver</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_private</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>create_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>update_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_width</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>folder_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_edit</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">favicon</span>\"
    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">demo/general/favicon.png</span>\"
    \"<span class=sf-dump-key>file_size</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"
    \"<span class=sf-dump-key>file_extension</span>\" => \"<span class=sf-dump-str title=\"3 characters\">png</span>\"
    \"<span class=sf-dump-key>driver</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_private</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>create_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>update_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_width</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>folder_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_edit</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: \"<span class=sf-dump-str title=\"11 characters\">Y-m-d H:i:s</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">slugField</span>: \"\"
  #<span class=sf-dump-protected title=\"Protected property\">slugFromField</span>: \"\"
  #<span class=sf-dump-protected title=\"Protected property\">cleanFields</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">seo_type</span>: <span class=sf-dump-const>null</span>
  +<span class=sf-dump-public title=\"Public property\">translationForeignKey</span>: \"<span class=sf-dump-str title=\"9 characters\">origin_id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">translation_class</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":7,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [booking.confirm-payment] not defined. at C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('booking.confirm...', Array, true)
#1 C:\\wamp64\\www\\mazar\\modules\\Booking\\Gateways\\PaystackGateway.php(71): route('booking.confirm...', Array)
#2 C:\\wamp64\\www\\mazar\\modules\\Booking\\SettingClass.php(25): Modules\\Booking\\Gateways\\PaystackGateway->getOptionsConfigs()
#3 [internal function]: Modules\\Booking\\SettingClass::getSettingPages()
#4 C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php(74): call_user_func(Array)
#5 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(61): Modules\\Core\\Models\\Settings::getSettingPages(true)
#6 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(238): Modules\\Core\\Helpers\\AdminMenuManager::all()
#7 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(249): Modules\\Core\\Helpers\\AdminMenuManager::menus()
#8 C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\parts\\sidebar.blade.php(2): Modules\\Core\\Helpers\\AdminMenuManager::groups_with_children()
#9 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\app.blade.php(50): Illuminate\\View\\View->render()
#17 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\wamp64\\www\\mazar\\resources\\views\\admin\\layouts\\app.blade.php(1): Illuminate\\View\\View->render()
#25 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#32 C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Views\\index.blade.php(195): Illuminate\\View\\View->render()
#33 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#39 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#41 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#43 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Dashboard.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Dashboard->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#94 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#95 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#96 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#97 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#98 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#99 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#100 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#101 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#102 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [booking.confirm-payment] not defined. at C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('booking.confirm...', Array, true)
#1 C:\\wamp64\\www\\mazar\\modules\\Booking\\Gateways\\PaystackGateway.php(71): route('booking.confirm...', Array)
#2 C:\\wamp64\\www\\mazar\\modules\\Booking\\SettingClass.php(25): Modules\\Booking\\Gateways\\PaystackGateway->getOptionsConfigs()
#3 [internal function]: Modules\\Booking\\SettingClass::getSettingPages()
#4 C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php(74): call_user_func(Array)
#5 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(61): Modules\\Core\\Models\\Settings::getSettingPages(true)
#6 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(238): Modules\\Core\\Helpers\\AdminMenuManager::all()
#7 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(249): Modules\\Core\\Helpers\\AdminMenuManager::menus()
#8 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\e60d865b1c6c7a38e17d492bfe5bc272.php(2): Modules\\Core\\Helpers\\AdminMenuManager::groups_with_children()
#9 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\bd82e218e126ae3390e6afb6b868d81b.php(52): Illuminate\\View\\View->render()
#17 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\01c27a945066b3a368a953501c2ee6b5.php(1): Illuminate\\View\\View->render()
#25 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#32 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\36ea131322d04d60a39f19d225fdeb43.php(199): Illuminate\\View\\View->render()
#33 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#39 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#41 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#43 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Dashboard.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Dashboard->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#94 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#95 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#96 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#97 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#98 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#99 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#100 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#101 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#102 {main}
"} 
[2025-07-08 00:12:00] local.ERROR: include(C:\wamp64\www\mazar\vendor\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory {"view":{"view":"C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\parts\\sidebar.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-747374317 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1784</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-747374317\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","recent_bookings":"<pre class=sf-dump id=sf-dump-1067507434 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2823</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1067507434\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","top_cards":"<pre class=sf-dump id=sf-dump-704831642 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total revenue</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"6 characters\">purple</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-cart</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Earning</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pink</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-gift</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bookings</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Total bookings</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"22 characters\">icon ion-ios-pricetags</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-704831642\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","earning_chart_data":"<pre class=sf-dump id=sf-dump-757548080 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>labels</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">07/07/2025</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">07/08/2025</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">07/09/2025</span>\"
  </samp>]
  \"<span class=sf-dump-key>datasets</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Revenue</span>\"
      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>backgroundColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#8892d6</span>\"
      \"<span class=sf-dump-key>stack</span>\" => \"<span class=sf-dump-str title=\"11 characters\">group-total</span>\"
    </samp>]
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>backgroundColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#F06292</span>\"
      \"<span class=sf-dump-key>stack</span>\" => \"<span class=sf-dump-str title=\"11 characters\">group-extra</span>\"
    </samp>]
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-757548080\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-2119474113 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total revenue</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"6 characters\">purple</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-cart</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Earning</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pink</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-gift</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bookings</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Total bookings</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"22 characters\">icon ion-ios-pricetags</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-2119474113\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","card":"<pre class=sf-dump id=sf-dump-1832149753 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
  \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
  \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
  \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
  \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1832149753\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-772147952 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-772147952\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","favicon":"<pre class=sf-dump id=sf-dump-675724053 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">10</span>\"
</pre><script>Sfdump(\"sf-dump-675724053\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","file":"<pre class=sf-dump id=sf-dump-2071625407 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Media\\Models\\MediaFile</span> {<a class=sf-dump-ref>#2885</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">media_files</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">favicon</span>\"
    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">demo/general/favicon.png</span>\"
    \"<span class=sf-dump-key>file_size</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"
    \"<span class=sf-dump-key>file_extension</span>\" => \"<span class=sf-dump-str title=\"3 characters\">png</span>\"
    \"<span class=sf-dump-key>driver</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_private</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>create_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>update_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_width</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>folder_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_edit</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">favicon</span>\"
    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">demo/general/favicon.png</span>\"
    \"<span class=sf-dump-key>file_size</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"
    \"<span class=sf-dump-key>file_extension</span>\" => \"<span class=sf-dump-str title=\"3 characters\">png</span>\"
    \"<span class=sf-dump-key>driver</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_private</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>create_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>update_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_width</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>folder_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_edit</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: \"<span class=sf-dump-str title=\"11 characters\">Y-m-d H:i:s</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">slugField</span>: \"\"
  #<span class=sf-dump-protected title=\"Protected property\">slugFromField</span>: \"\"
  #<span class=sf-dump-protected title=\"Protected property\">cleanFields</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">seo_type</span>: <span class=sf-dump-const>null</span>
  +<span class=sf-dump-public title=\"Public property\">translationForeignKey</span>: \"<span class=sf-dump-str title=\"9 characters\">origin_id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">translation_class</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-2071625407\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":7,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): include(C:\\wamp64\\www\\mazar\\vendor\\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory at C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#1 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#2 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): include('C:\\\\wamp64\\\\www\\\\m...')
#3 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('C:\\\\wamp64\\\\www\\\\m...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\Support...')
#5 C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php(69): class_exists('Modules\\\\Support...')
#6 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(61): Modules\\Core\\Models\\Settings::getSettingPages(true)
#7 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(238): Modules\\Core\\Helpers\\AdminMenuManager::all()
#8 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(249): Modules\\Core\\Helpers\\AdminMenuManager::menus()
#9 C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\parts\\sidebar.blade.php(2): Modules\\Core\\Helpers\\AdminMenuManager::groups_with_children()
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\app.blade.php(50): Illuminate\\View\\View->render()
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\wamp64\\www\\mazar\\resources\\views\\admin\\layouts\\app.blade.php(1): Illuminate\\View\\View->render()
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Views\\index.blade.php(195): Illuminate\\View\\View->render()
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#39 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#41 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#43 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#46 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Dashboard.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Dashboard->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#78 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#101 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#102 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#103 {main}

[previous exception] [object] (ErrorException(code: 0): include(C:\\wamp64\\www\\mazar\\vendor\\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory at C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#1 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#2 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): include('C:\\\\wamp64\\\\www\\\\m...')
#3 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('C:\\\\wamp64\\\\www\\\\m...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\Support...')
#5 C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php(69): class_exists('Modules\\\\Support...')
#6 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(61): Modules\\Core\\Models\\Settings::getSettingPages(true)
#7 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(238): Modules\\Core\\Helpers\\AdminMenuManager::all()
#8 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(249): Modules\\Core\\Helpers\\AdminMenuManager::menus()
#9 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\e60d865b1c6c7a38e17d492bfe5bc272.php(2): Modules\\Core\\Helpers\\AdminMenuManager::groups_with_children()
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\bd82e218e126ae3390e6afb6b868d81b.php(52): Illuminate\\View\\View->render()
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\01c27a945066b3a368a953501c2ee6b5.php(1): Illuminate\\View\\View->render()
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\36ea131322d04d60a39f19d225fdeb43.php(199): Illuminate\\View\\View->render()
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#39 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#41 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#43 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#46 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Dashboard.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Dashboard->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#78 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#101 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#102 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#103 {main}
"} 
[2025-07-08 00:15:58] local.ERROR: include(C:\wamp64\www\mazar\vendor\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory {"view":{"view":"C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\parts\\sidebar.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-245428674 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1784</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-245428674\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","recent_bookings":"<pre class=sf-dump id=sf-dump-930045543 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2823</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-930045543\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","top_cards":"<pre class=sf-dump id=sf-dump-1110080754 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total revenue</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"6 characters\">purple</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-cart</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Earning</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pink</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-gift</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bookings</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Total bookings</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"22 characters\">icon ion-ios-pricetags</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1110080754\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","earning_chart_data":"<pre class=sf-dump id=sf-dump-2014782028 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>labels</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">07/07/2025</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">07/08/2025</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">07/09/2025</span>\"
  </samp>]
  \"<span class=sf-dump-key>datasets</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Revenue</span>\"
      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>backgroundColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#8892d6</span>\"
      \"<span class=sf-dump-key>stack</span>\" => \"<span class=sf-dump-str title=\"11 characters\">group-total</span>\"
    </samp>]
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>backgroundColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#F06292</span>\"
      \"<span class=sf-dump-key>stack</span>\" => \"<span class=sf-dump-str title=\"11 characters\">group-extra</span>\"
    </samp>]
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-2014782028\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-415253089 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total revenue</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"6 characters\">purple</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-cart</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Earning</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pink</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-gift</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bookings</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Total bookings</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"22 characters\">icon ion-ios-pricetags</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-415253089\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","card":"<pre class=sf-dump id=sf-dump-292981010 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
  \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
  \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
  \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
  \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-292981010\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1236885601 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1236885601\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","favicon":"<pre class=sf-dump id=sf-dump-1847180786 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">10</span>\"
</pre><script>Sfdump(\"sf-dump-1847180786\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","file":"<pre class=sf-dump id=sf-dump-643050726 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Media\\Models\\MediaFile</span> {<a class=sf-dump-ref>#2885</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">media_files</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">favicon</span>\"
    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">demo/general/favicon.png</span>\"
    \"<span class=sf-dump-key>file_size</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"
    \"<span class=sf-dump-key>file_extension</span>\" => \"<span class=sf-dump-str title=\"3 characters\">png</span>\"
    \"<span class=sf-dump-key>driver</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_private</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>create_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>update_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_width</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>folder_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_edit</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">favicon</span>\"
    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">demo/general/favicon.png</span>\"
    \"<span class=sf-dump-key>file_size</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"
    \"<span class=sf-dump-key>file_extension</span>\" => \"<span class=sf-dump-str title=\"3 characters\">png</span>\"
    \"<span class=sf-dump-key>driver</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_private</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>create_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>update_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_width</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>folder_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_edit</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: \"<span class=sf-dump-str title=\"11 characters\">Y-m-d H:i:s</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">slugField</span>: \"\"
  #<span class=sf-dump-protected title=\"Protected property\">slugFromField</span>: \"\"
  #<span class=sf-dump-protected title=\"Protected property\">cleanFields</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">seo_type</span>: <span class=sf-dump-const>null</span>
  +<span class=sf-dump-public title=\"Public property\">translationForeignKey</span>: \"<span class=sf-dump-str title=\"9 characters\">origin_id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">translation_class</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-643050726\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":7,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): include(C:\\wamp64\\www\\mazar\\vendor\\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory at C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#1 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#2 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): include('C:\\\\wamp64\\\\www\\\\m...')
#3 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('C:\\\\wamp64\\\\www\\\\m...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\Support...')
#5 C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php(69): class_exists('Modules\\\\Support...')
#6 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(61): Modules\\Core\\Models\\Settings::getSettingPages(true)
#7 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(238): Modules\\Core\\Helpers\\AdminMenuManager::all()
#8 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(249): Modules\\Core\\Helpers\\AdminMenuManager::menus()
#9 C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\parts\\sidebar.blade.php(2): Modules\\Core\\Helpers\\AdminMenuManager::groups_with_children()
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\app.blade.php(50): Illuminate\\View\\View->render()
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\wamp64\\www\\mazar\\resources\\views\\admin\\layouts\\app.blade.php(1): Illuminate\\View\\View->render()
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Views\\index.blade.php(195): Illuminate\\View\\View->render()
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#39 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#41 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#43 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#46 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Dashboard.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Dashboard->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#78 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#101 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#102 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#103 {main}

[previous exception] [object] (ErrorException(code: 0): include(C:\\wamp64\\www\\mazar\\vendor\\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory at C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#1 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#2 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): include('C:\\\\wamp64\\\\www\\\\m...')
#3 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('C:\\\\wamp64\\\\www\\\\m...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\Support...')
#5 C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php(69): class_exists('Modules\\\\Support...')
#6 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(61): Modules\\Core\\Models\\Settings::getSettingPages(true)
#7 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(238): Modules\\Core\\Helpers\\AdminMenuManager::all()
#8 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(249): Modules\\Core\\Helpers\\AdminMenuManager::menus()
#9 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\e60d865b1c6c7a38e17d492bfe5bc272.php(2): Modules\\Core\\Helpers\\AdminMenuManager::groups_with_children()
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\bd82e218e126ae3390e6afb6b868d81b.php(52): Illuminate\\View\\View->render()
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\01c27a945066b3a368a953501c2ee6b5.php(1): Illuminate\\View\\View->render()
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\36ea131322d04d60a39f19d225fdeb43.php(199): Illuminate\\View\\View->render()
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#39 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#41 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#43 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#46 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Dashboard.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Dashboard->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#78 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#101 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#102 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#103 {main}
"} 
[2025-07-08 00:20:23] local.ERROR: include(C:\wamp64\www\mazar\vendor\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory {"view":{"view":"C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\parts\\sidebar.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1496000562 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1784</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1496000562\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","recent_bookings":"<pre class=sf-dump id=sf-dump-2130966670 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2823</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-2130966670\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","top_cards":"<pre class=sf-dump id=sf-dump-836070073 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total revenue</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"6 characters\">purple</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-cart</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Earning</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pink</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-gift</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bookings</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Total bookings</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"22 characters\">icon ion-ios-pricetags</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-836070073\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","earning_chart_data":"<pre class=sf-dump id=sf-dump-422610274 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>labels</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">07/07/2025</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">07/08/2025</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">07/09/2025</span>\"
  </samp>]
  \"<span class=sf-dump-key>datasets</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Revenue</span>\"
      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>backgroundColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#8892d6</span>\"
      \"<span class=sf-dump-key>stack</span>\" => \"<span class=sf-dump-str title=\"11 characters\">group-total</span>\"
    </samp>]
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>backgroundColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#F06292</span>\"
      \"<span class=sf-dump-key>stack</span>\" => \"<span class=sf-dump-str title=\"11 characters\">group-extra</span>\"
    </samp>]
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-422610274\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-364937664 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total revenue</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"6 characters\">purple</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-cart</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Earning</span>\"
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">$0</span>\"
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Total Earning</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pink</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"17 characters\">icon ion-ios-gift</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bookings</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Total bookings</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"22 characters\">icon ion-ios-pricetags</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
    \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
    \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-364937664\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","card":"<pre class=sf-dump id=sf-dump-559974475 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-num>6</span>
  \"<span class=sf-dump-key>size_md</span>\" => <span class=sf-dump-num>3</span>
  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"
  \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>63</span>
  \"<span class=sf-dump-key>desc</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Total bookable services</span>\"
  \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"
  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"18 characters\">icon ion-ios-flash</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-559974475\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1327119982 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1327119982\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","favicon":"<pre class=sf-dump id=sf-dump-1893867658 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">10</span>\"
</pre><script>Sfdump(\"sf-dump-1893867658\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","file":"<pre class=sf-dump id=sf-dump-342907071 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Media\\Models\\MediaFile</span> {<a class=sf-dump-ref>#2885</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">media_files</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">favicon</span>\"
    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">demo/general/favicon.png</span>\"
    \"<span class=sf-dump-key>file_size</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"
    \"<span class=sf-dump-key>file_extension</span>\" => \"<span class=sf-dump-str title=\"3 characters\">png</span>\"
    \"<span class=sf-dump-key>driver</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_private</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>create_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>update_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_width</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>folder_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_edit</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">favicon</span>\"
    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"24 characters\">demo/general/favicon.png</span>\"
    \"<span class=sf-dump-key>file_size</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"
    \"<span class=sf-dump-key>file_extension</span>\" => \"<span class=sf-dump-str title=\"3 characters\">png</span>\"
    \"<span class=sf-dump-key>driver</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_private</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>create_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>update_user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>app_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_width</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>folder_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>file_edit</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: \"<span class=sf-dump-str title=\"11 characters\">Y-m-d H:i:s</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">slugField</span>: \"\"
  #<span class=sf-dump-protected title=\"Protected property\">slugFromField</span>: \"\"
  #<span class=sf-dump-protected title=\"Protected property\">cleanFields</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">seo_type</span>: <span class=sf-dump-const>null</span>
  +<span class=sf-dump-public title=\"Public property\">translationForeignKey</span>: \"<span class=sf-dump-str title=\"9 characters\">origin_id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">translation_class</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-342907071\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":7,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): include(C:\\wamp64\\www\\mazar\\vendor\\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory at C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#1 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#2 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): include('C:\\\\wamp64\\\\www\\\\m...')
#3 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('C:\\\\wamp64\\\\www\\\\m...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\Support...')
#5 C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php(69): class_exists('Modules\\\\Support...')
#6 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(61): Modules\\Core\\Models\\Settings::getSettingPages(true)
#7 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(238): Modules\\Core\\Helpers\\AdminMenuManager::all()
#8 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(249): Modules\\Core\\Helpers\\AdminMenuManager::menus()
#9 C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\parts\\sidebar.blade.php(2): Modules\\Core\\Helpers\\AdminMenuManager::groups_with_children()
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\wamp64\\www\\mazar\\modules\\Layout\\admin\\app.blade.php(50): Illuminate\\View\\View->render()
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\wamp64\\www\\mazar\\resources\\views\\admin\\layouts\\app.blade.php(1): Illuminate\\View\\View->render()
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Views\\index.blade.php(195): Illuminate\\View\\View->render()
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#39 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#41 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#43 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#46 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Dashboard.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Dashboard->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#78 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#101 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#102 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#103 {main}

[previous exception] [object] (ErrorException(code: 0): include(C:\\wamp64\\www\\mazar\\vendor\\composer/../../modules/Support/SettingClass.php): Failed to open stream: No such file or directory at C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#1 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\wamp...', 'C:\\\\wamp64\\\\www\\\\m...', 571)
#2 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(571): include('C:\\\\wamp64\\\\www\\\\m...')
#3 C:\\wamp64\\www\\mazar\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('C:\\\\wamp64\\\\www\\\\m...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\Support...')
#5 C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php(69): class_exists('Modules\\\\Support...')
#6 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(61): Modules\\Core\\Models\\Settings::getSettingPages(true)
#7 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(238): Modules\\Core\\Helpers\\AdminMenuManager::all()
#8 C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php(249): Modules\\Core\\Helpers\\AdminMenuManager::menus()
#9 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\e60d865b1c6c7a38e17d492bfe5bc272.php(2): Modules\\Core\\Helpers\\AdminMenuManager::groups_with_children()
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\bd82e218e126ae3390e6afb6b868d81b.php(52): Illuminate\\View\\View->render()
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\01c27a945066b3a368a953501c2ee6b5.php(1): Illuminate\\View\\View->render()
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\36ea131322d04d60a39f19d225fdeb43.php(199): Illuminate\\View\\View->render()
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#39 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#41 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#43 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#46 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Dashboard.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Dashboard->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#78 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#101 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#102 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#103 {main}
"} 
[2025-07-08 21:11:26] local.ERROR: Route [booking.doCheckout] not defined. {"view":{"view":"C:\\wamp64\\www\\mazar\\modules\\Layout\\parts\\global-script.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1508</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [booking.doCheckout] not defined. at C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('booking.doCheck...', Array, true)
#1 C:\\wamp64\\www\\mazar\\modules\\Layout\\parts\\global-script.blade.php(24): route('booking.doCheck...')
#2 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#3 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#5 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#6 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#7 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\wamp64\\www\\mazar\\modules\\Layout\\app.blade.php(32): Illuminate\\View\\View->render()
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\wamp64\\www\\mazar\\resources\\views\\layouts\\app.blade.php(1): Illuminate\\View\\View->render()
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\wamp64\\www\\mazar\\resources\\views\\auth\\login.blade.php(15): Illuminate\\View\\View->render()
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Laravel\\Fortify\\Http\\Responses\\SimpleViewResponse))
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#41 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#94 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#95 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [booking.doCheckout] not defined. at C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('booking.doCheck...', Array, true)
#1 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\7b18175a9723c851ff7cebb606c59e40.php(24): route('booking.doCheck...')
#2 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#3 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#5 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#6 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#7 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\784656b30d3ab2b348e2146c133ac916.php(34): Illuminate\\View\\View->render()
#10 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#11 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#13 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#14 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#15 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\7b8d86fc274382aae8e4d21280ada9de.php(1): Illuminate\\View\\View->render()
#18 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#19 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#21 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#22 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#23 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\wamp64\\www\\mazar\\storage\\framework\\views\\fb7bd89df7518aa9b8fa71c15fc53f10.php(17): Illuminate\\View\\View->render()
#26 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\wamp64\\\\www\\\\m...')
#27 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\wamp64\\\\www\\\\m...', Array)
#29 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\wamp64\\\\www\\\\m...', Array)
#30 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\wamp64\\\\www\\\\m...', Array)
#31 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#34 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#35 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#36 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#37 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Laravel\\Fortify\\Http\\Responses\\SimpleViewResponse))
#38 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#41 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RequireChangePassword.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequireChangePassword->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetLanguageForAdmin.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguageForAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\SetCurrentCurrency.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetCurrentCurrency->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HideDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectForMultiLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#59 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#67 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#68 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#69 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#70 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\wamp64\\www\\mazar\\themes\\Base\\Core\\Middleware\\RunUpdater.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\wamp64\\www\\mazar\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#93 C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#94 C:\\wamp64\\www\\mazar\\public\\index.php(77): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#95 {main}
"} 
