@extends('admin.layouts.app')
@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between mb20">
            <h1 class="title-bar">{{__("All Modules")}}</h1>
        </div>
        @include('admin.message')
        <div class="filter-div d-flex justify-content-between ">
            <div class="col-left">
                @if(!empty($rows))
                    <form method="post" action="{{route('core.admin.module.bulkEdit')}}" class="filter-form filter-form-left d-flex justify-content-start">
                        {{csrf_field()}}
                        <select name="action" class="form-control">
                            <option value="">{{__(" Bulk Actions ")}}</option>
                            <option value="enable">{{__("Enable")}}</option>
                            <option value="disable">{{__("Disable")}}</option>
                        </select>
                        <button class="btn-info btn btn-icon dungdt-apply-form-btn" type="button">{{__('Apply')}}</button>
                    </form>
                @endif
            </div>
            {{--<div class="col-left">
                <form method="get" action="{{route('core.admin.plugins.index')}} " class="filter-form filter-form-right d-flex justify-content-end flex-column flex-sm-row" role="search">
                    <input type="text" name="s" value="{{ Request()->s }}" placeholder="{{__('Search by name')}}" class="form-control">
                    <button class="btn-info btn btn-icon btn_search" type="submit">{{__('Search')}}</button>
                </form>
            </div>--}}
        </div>
        <div class="panel">
            <div class="panel-body">
                <form action="" class="bravo-form-item">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                            <tr>
                                <th width="60px"><input type="checkbox" class="check-all"></th>
                                <th width="200px"> {{ __('Module name')}}</th>
                                <th > {{ __('Description')}}</th>
                                <th width="130px"> {{ __('Author')}}</th>
                                <th width="100px"> {{ __('Version')}}</th>
                                <th width="100px"> {{ __('Status')}}</th>
                                <th width="100px"> {{ __('Actions')}}</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if(!empty($rows))
                                @foreach($rows as $id=>$row)
                                    <tr class="">
                                        <td><input type="checkbox" name="ids[]" class="check-item" value="{{$id}}">
                                        </td>
                                        <td class="title">
                                            <div class="d-flex align-items-center">
                                                @if($thumb = $row::getThumb())
                                                    <div class="mr-3">
                                                        <img src="{{$thumb}}" width="50" height="50" alt="">
                                                    </div>
                                                @endif
                                                <a href="#">{{$row::getName()}}</a>
                                            </div>
                                        </td>
                                        <td>
                                            {{$row::getDesc()}}
                                        </td>
                                        <td>
                                            {{$row::getAuthor()}}
                                        </td>
                                        <td>
                                            {{$row::getVersion()}}
                                        </td>
                                        <td>
                                            @php
                                                $isDisabled = setting_item(strtolower($id) . '_disable', false);
                                                $isEnabled = !$isDisabled;
                                            @endphp
                                            @if($isEnabled)
                                                <span class="badge badge-success">{{__("Enabled")}}</span>
                                            @else
                                                <span class="badge badge-secondary">{{__("Disabled")}}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-toggle="dropdown">
                                                    {{__("Actions")}}
                                                </button>
                                                <div class="dropdown-menu">
                                                    @php
                                                        $isDisabled = setting_item(strtolower($id) . '_disable', false);
                                                        $isEnabled = !$isDisabled;
                                                    @endphp
                                                    @if($isEnabled)
                                                        <a class="dropdown-item module-toggle" href="#" data-module="{{$id}}" data-action="disable">
                                                            <i class="fa fa-pause"></i> {{__("Disable")}}
                                                        </a>
                                                    @else
                                                        <a class="dropdown-item module-toggle" href="#" data-module="{{$id}}" data-action="enable">
                                                            <i class="fa fa-play"></i> {{__("Enable")}}
                                                        </a>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="7">{{__("No Module found")}}</td>
                                </tr>
                            @endif
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('script.body')
<script>
$(document).ready(function() {
    // Handle module toggle
    $('.module-toggle').on('click', function(e) {
        e.preventDefault();

        var $this = $(this);
        var moduleId = $this.data('module');
        var action = $this.data('action');
        var $row = $this.closest('tr');

        // Show loading state
        $this.html('<i class="fa fa-spinner fa-spin"></i> ' + (action === 'enable' ? '{{__("Enabling...")}}' : '{{__("Disabling...")}}'));
        $this.addClass('disabled');

        $.ajax({
            url: '{{route("core.admin.module.toggle", ":moduleId")}}'.replace(':moduleId', moduleId),
            method: 'POST',
            data: {
                _token: '{{csrf_token()}}'
            },
            success: function(response) {
                if (response.success) {
                    // Update status badge
                    var $statusCell = $row.find('td:nth-child(6)');
                    if (response.status) {
                        $statusCell.html('<span class="badge badge-success">{{__("Enabled")}}</span>');
                    } else {
                        $statusCell.html('<span class="badge badge-secondary">{{__("Disabled")}}</span>');
                    }

                    // Update action button
                    var $actionsCell = $row.find('td:nth-child(7)');
                    if (response.status) {
                        $actionsCell.html(`
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-toggle="dropdown">
                                    {{__("Actions")}}
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item module-toggle" href="#" data-module="${moduleId}" data-action="disable">
                                        <i class="fa fa-pause"></i> {{__("Disable")}}
                                    </a>
                                </div>
                            </div>
                        `);
                    } else {
                        $actionsCell.html(`
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-toggle="dropdown">
                                    {{__("Actions")}}
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item module-toggle" href="#" data-module="${moduleId}" data-action="enable">
                                        <i class="fa fa-play"></i> {{__("Enable")}}
                                    </a>
                                </div>
                            </div>
                        `);
                    }

                    // Re-bind event handlers for new buttons
                    $actionsCell.find('.module-toggle').on('click', arguments.callee);

                    // Show success message
                    showMessage('success', response.message);
                } else {
                    showMessage('error', response.message || '{{__("An error occurred")}}');
                }
            },
            error: function(xhr) {
                var message = '{{__("An error occurred")}}';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    message = xhr.responseJSON.error;
                }
                showMessage('error', message);
            },
            complete: function() {
                // Reset button state
                $this.removeClass('disabled');
            }
        });
    });

    function showMessage(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // Remove existing alerts
        $('.container-fluid .alert').remove();

        // Add new alert after the title bar
        $('.title-bar').parent().after(alertHtml);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
@endsection
