<?php

namespace Modules\Support;

use <PERSON><PERSON>les\Core\Helpers\AdminMenuManager;
use <PERSON><PERSON><PERSON>\ModuleServiceProvider;
use Modules\User\Helpers\PermissionHelper;
use Modules\Support\Models\SupportModule;

class ModuleProvider extends ModuleServiceProvider
{


    public function boot()
    {
        if (is_installed() and SupportModule::isEnable()) {
            $this->loadMigrationsFrom(__DIR__ . '/Database/Migrations');
            $this->mergeConfigFrom(__DIR__ . '/Configs/config.php', 'support');
            $this->loadViewsFrom(__DIR__ . '/Views', 'Support');

            AdminMenuManager::register_group('support', __("Support Center"), 160);
        }

        PermissionHelper::add([
            'support_topic_view',
            'support_topic_create',
            'support_topic_update',
            'support_topic_delete',

            'support_topic_category',

            // Tickets
            'support_ticket_view',
            'support_ticket_create',
            'support_ticket_update',
            'support_ticket_delete',
            'support_ticket_reply',
            'support_ticket_category',
            'support_ticket_manage',
        ]);
    }

    public function register()
    {
        parent::register(); // TODO: Change the autogenerated stub
        $this->app->register(RouterServiceProvider::class);
    }

    public static function getAdminMenu()
    {
        if (!SupportModule::isEnable()) return [];

        return [
            'ticket' => [
                "position"   => 10,
                'url'        => route('support.admin.ticket.index'),
                'title'      => __("Tickets"),
                'icon'       => 'ion-md-bookmarks',
                'permission' => 'support_ticket_view',
                'group'      => 'support',
                'children'   => [
                    'view' => [
                        'url'        => route('support.admin.ticket.index'),
                        'title'      => __("All Tickets"),
                        'permission' => 'support_ticket_view',
                    ],
                    'cat'  => [
                        'url'        => route('support.admin.ticket.category.index'),
                        'title'      => __("Categories"),
                        'permission' => 'support_ticket_manage',
                    ],
                ]
            ],
            'topic'  => [
                "position"   => 10,
                'url'        => route('support.admin.topic.index'),
                'title'      => __("Topics"),
                'icon'       => 'ion-md-bookmarks',
                'permission' => 'support_topic_view',
                'group'      => 'support',
                'children'   => [
                    'view'   => [
                        'url'        => route('support.admin.topic.index'),
                        'title'      => __("All Topics"),
                        'permission' => 'support_topic_view',
                    ],
                    'create' => [
                        'url'        => route('support.admin.topic.create'),
                        'title'      => __("Add Topic"),
                        'permission' => 'support_topic_create',
                    ],
                    'cat'    => [
                        'url'        => route('support.admin.topic.category.index'),
                        'title'      => __("Categories"),
                        'permission' => 'support_topic_create',
                    ],
                    'tag'    => [
                        'url'        => route('support.admin.topic.tag.index'),
                        'title'      => __("Tags"),
                        'permission' => 'support_topic_create',
                    ],
                ]
            ],
        ];
    }

    public static function getUserMenu()
    {
        if (!SupportModule::isEnable()) return [];

        return [
            'support' => [
                'url'   => route('support.index'),
                'title' => __("Support Center"),
            ]
        ];
    }

    public static function getName()
    {
        return 'Support';
    }

    public static function getDesc()
    {
        return __('Support ticket and knowledge base system');
    }

    public static function getVersion()
    {
        return '1.0';
    }

    public static function getAuthor()
    {
        return 'MAZAR Travel';
    }
}
