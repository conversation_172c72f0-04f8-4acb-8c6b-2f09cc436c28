<?php

namespace Modules\Booking;

use <PERSON><PERSON><PERSON>\ModuleServiceProvider;
use Modules\Booking\Models\BookingModule;

class ModuleProvider extends ModuleServiceProvider
{

    public function boot()
    {
        if (is_installed() and BookingModule::isEnable()) {
            $this->loadViewsFrom(__DIR__ . '/Views', 'Booking');
        }
    }

    public function register()
    {
        $this->app->register(RouterServiceProvider::class);
        parent::register();
    }

    public static function getAdminMenu()
    {
        if (!BookingModule::isEnable()) return [];

        return [
            'booking' => [
                'title' => __("Booking Management"),
                'url' => '#',
                "position" => 50,
                'icon' => "fa fa-calendar-check-o",
                "group" => "booking",
            ]
        ];
    }

    public static function getName()
    {
        return 'Booking';
    }

    public static function getDesc()
    {
        return __('Booking and payment management system');
    }

    public static function getVersion()
    {
        return '1.0';
    }

    public static function getAuthor()
    {
        return 'MAZAR Travel';
    }
}
