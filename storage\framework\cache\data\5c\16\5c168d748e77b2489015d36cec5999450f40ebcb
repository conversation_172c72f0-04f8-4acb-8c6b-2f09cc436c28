9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:32:"Modules\Language\Models\Language":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"core_languages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:6:"locale";s:2:"en";s:4:"name";s:7:"English";s:4:"flag";s:2:"gb";s:6:"status";s:7:"publish";s:11:"create_user";i:1;s:11:"update_user";N;s:13:"last_build_at";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-07-04 18:17:11";s:10:"updated_at";s:19:"2025-07-04 18:17:11";}s:11:" * original";a:11:{s:2:"id";i:1;s:6:"locale";s:2:"en";s:4:"name";s:7:"English";s:4:"flag";s:2:"gb";s:6:"status";s:7:"publish";s:11:"create_user";i:1;s:11:"update_user";N;s:13:"last_build_at";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-07-04 18:17:11";s:10:"updated_at";s:19:"2025-07-04 18:17:11";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";s:11:"Y-m-d H:i:s";s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:6:"locale";i:1;s:4:"name";i:2;s:6:"active";i:3;s:4:"flag";i:4;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:12:" * slugField";s:0:"";s:16:" * slugFromField";s:0:"";s:14:" * cleanFields";a:0:{}s:11:" * seo_type";N;s:21:"translationForeignKey";s:9:"origin_id";s:20:" * translation_class";N;s:16:" * forceDeleting";b:0;}i:1;O:32:"Modules\Language\Models\Language":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"core_languages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:2;s:6:"locale";s:2:"ja";s:4:"name";s:8:"Japanese";s:4:"flag";s:2:"jp";s:6:"status";s:7:"publish";s:11:"create_user";i:1;s:11:"update_user";N;s:13:"last_build_at";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-07-04 18:17:11";s:10:"updated_at";s:19:"2025-07-04 18:17:11";}s:11:" * original";a:11:{s:2:"id";i:2;s:6:"locale";s:2:"ja";s:4:"name";s:8:"Japanese";s:4:"flag";s:2:"jp";s:6:"status";s:7:"publish";s:11:"create_user";i:1;s:11:"update_user";N;s:13:"last_build_at";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-07-04 18:17:11";s:10:"updated_at";s:19:"2025-07-04 18:17:11";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";s:11:"Y-m-d H:i:s";s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:6:"locale";i:1;s:4:"name";i:2;s:6:"active";i:3;s:4:"flag";i:4;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:12:" * slugField";s:0:"";s:16:" * slugFromField";s:0:"";s:14:" * cleanFields";a:0:{}s:11:" * seo_type";N;s:21:"translationForeignKey";s:9:"origin_id";s:20:" * translation_class";N;s:16:" * forceDeleting";b:0;}i:2;O:32:"Modules\Language\Models\Language":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"core_languages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:3;s:6:"locale";s:3:"egy";s:4:"name";s:8:"Egyptian";s:4:"flag";s:2:"eg";s:6:"status";s:7:"publish";s:11:"create_user";i:1;s:11:"update_user";N;s:13:"last_build_at";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-07-04 18:17:11";s:10:"updated_at";s:19:"2025-07-04 18:17:11";}s:11:" * original";a:11:{s:2:"id";i:3;s:6:"locale";s:3:"egy";s:4:"name";s:8:"Egyptian";s:4:"flag";s:2:"eg";s:6:"status";s:7:"publish";s:11:"create_user";i:1;s:11:"update_user";N;s:13:"last_build_at";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-07-04 18:17:11";s:10:"updated_at";s:19:"2025-07-04 18:17:11";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";s:11:"Y-m-d H:i:s";s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:6:"locale";i:1;s:4:"name";i:2;s:6:"active";i:3;s:4:"flag";i:4;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:12:" * slugField";s:0:"";s:16:" * slugFromField";s:0:"";s:14:" * cleanFields";a:0:{}s:11:" * seo_type";N;s:21:"translationForeignKey";s:9:"origin_id";s:20:" * translation_class";N;s:16:" * forceDeleting";b:0;}i:3;O:32:"Modules\Language\Models\Language":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"core_languages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:4;s:6:"locale";s:2:"fr";s:4:"name";s:2:"Fr";s:4:"flag";s:2:"FR";s:6:"status";s:7:"publish";s:11:"create_user";i:7;s:11:"update_user";i:7;s:13:"last_build_at";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-07-05 09:35:28";s:10:"updated_at";s:19:"2025-07-05 09:35:48";}s:11:" * original";a:11:{s:2:"id";i:4;s:6:"locale";s:2:"fr";s:4:"name";s:2:"Fr";s:4:"flag";s:2:"FR";s:6:"status";s:7:"publish";s:11:"create_user";i:7;s:11:"update_user";i:7;s:13:"last_build_at";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-07-05 09:35:28";s:10:"updated_at";s:19:"2025-07-05 09:35:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";s:11:"Y-m-d H:i:s";s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:6:"locale";i:1;s:4:"name";i:2;s:6:"active";i:3;s:4:"flag";i:4;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:12:" * slugField";s:0:"";s:16:" * slugFromField";s:0:"";s:14:" * cleanFields";a:0:{}s:11:" * seo_type";N;s:21:"translationForeignKey";s:9:"origin_id";s:20:" * translation_class";N;s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}