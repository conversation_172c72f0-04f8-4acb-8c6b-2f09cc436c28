{"The provided password does not match your current password.": "", "-- Select --": "-- Auswählen --", ":num mins": ":num Min", ":num min": ":num Min", ":num hours": ":num Std", ":num hour": ":num Std", ":num days": ":num Tage", ":num day": ":num Tag", ":num weeks": ":num <PERSON><PERSON>en", ":num week": ":num W<PERSON>e", ":num months": ":num Monate", ":num month": ":num <PERSON><PERSON>", ":num years": ":num Jahre", ":num year": ":num Jahr", "sqft": "m²", "Draft": "<PERSON><PERSON><PERSON><PERSON>", "Unpaid": "Unbezahlt", "Paid": "Be<PERSON>hlt", "Processing": "In Bearbeitung", "Completed": "Abgeschlossen", "Confirmed": "Bestätigt", "Cancelled": "<PERSON><PERSON><PERSON><PERSON>", "Cancel": "Abbrechen", "Pending": "<PERSON><PERSON><PERSON><PERSON>", "Partial Payment": "Teilzahlung", "Failed": "Fehlgeschlagen", "Phone": "Telefon", "Number": "<PERSON><PERSON><PERSON>", "Email": "E-Mail", "Attachment": "<PERSON><PERSON>", "Multi Attachments": "<PERSON><PERSON><PERSON>", "Text": "Text", "D": "T", "H": "S", ":count Days": ":count <PERSON><PERSON>", ":count Day": ":count Tag", ":count Hours": ":count <PERSON><PERSON><PERSON>", ":count Hour": ":count <PERSON><PERSON><PERSON>", "Show on the map": "Au<PERSON> der Karte anzeigen", "Login": "Anmelden", "Can not authorize": "Autorisierung nicht möglich", "Email :email exists. Can not register new account with your social email": "E-Mail :email existiert bereits. Neues Konto mit Ihrer Social-Media-E-Mail kann nicht registriert werden", "User blocked": "<PERSON><PERSON><PERSON> g<PERSON>", "Your account has been blocked": "<PERSON><PERSON> Konto wurde gesperrt", "Sign Up": "Registrieren", "News": "Nachrichten", "Access denied for user!. Please check your configuration.": "Zugriff für Benutzer verweigert! Bitte überprüfen Sie Ihre Konfiguration.", "Yes! Successfully connected to the DB: \".DB::connection()->getDatabaseName()));\r\n            }else{\r\n                return $this->sendSuccess(false , __(\"Could not find the database. Please check your configuration.": "Ja! Erfolgreich mit der Datenbank verbunden: \".DB::connection()->getDatabaseName()));\r\n            }else{\r\n                return $this->sendSuccess(false , __(\"Datenbank nicht gefunden. Bitte überprüfen Sie Ihre Konfiguration.", "For security, please change your password to continue": "Aus Sicherheitsgründen ändern Sie bitte Ihr Passwort, um fortzufahren", "Upgrade for :price": "Upgrade für :price", "Please verify captcha.": "Bitte Captcha verifizieren.", "Publish": "Veröffentlichen", "Blocked": "<PERSON><PERSON><PERSON><PERSON>", "Manage Agencies": "<PERSON><PERSON> ver<PERSON>", "All": "Alle", "Item not found": "Element nicht gefunden", "Create Agency": "<PERSON><PERSON> er<PERSON>", "Create a Agency": "Eine Agentur erstellen", "Agent only belong one agencies": "Agent g<PERSON><PERSON><PERSON> nur zu einer Agentur", "Agency updated": "Agentur aktualisiert", "Agency created": "<PERSON><PERSON> er<PERSON>", "No items selected!": "Keine Elemente ausgewählt!", "Please select an action!": "Bitte wählen Si<PERSON> eine Aktion!", "Delete success!": "Erfolgreich gelöscht!", "Update success!": "Erfolgreich aktualisiert!", "System error": "<PERSON><PERSON><PERSON>", "Title": "Titel", "Sub Title": "Untertitel", "List Item(s)": "Listenelement(e)", "Name": "Name", "Type": "<PERSON><PERSON>", "Avatar Image": "Avatar-Bild", "Our Team": "Unser Team", "Image": "Bild", "Our Partners": "Unsere Partner", "Agent Register Form": "Agent-Registrierungsformular", "Our Agencies": "Unsere Agenturen", "Manage Agency": "<PERSON><PERSON> ver<PERSON><PERSON>", "Agency not found!": "Agentur nicht gefunden!", "Edit Agency :name": "Agentur :name bearbeiten", "Edit Agency": "<PERSON><PERSON> bearbeiten", "Agency fail": "Agentur<PERSON><PERSON><PERSON>", "Manage Agent": "Agent ve<PERSON><PERSON><PERSON>", "Agency :name": "Agentur :name", "All Agent": "Alle Agenten", "Email is required field": "E-Mail ist ein Pflichtfeld", "Email invalidate": "E-Mail ungültig", "Password is required field": "Passwort ist ein Pflich<PERSON>feld", "The first name is required field": "Der Vorname ist ein Pflichtfeld", "The last name is required field": "Der Nachname ist ein Pflichtfeld", "The business name is required field": "Der Firmenname ist ein Pflichtfeld", "Can not register": "Registrierung nicht möglich", "Register success": "Registrierung erfolgreich", "Register success. Please wait for admin approval": "Registrierung erfolgreich. Bitte warten Sie auf die Admin-Genehmigung", "Success": "Erfolgreich", "Find Agents": "<PERSON><PERSON> finden", "Thank you for contacting us! We will get back to you soon": "Vielen Dank für Ihre Kontaktaufnahme! Wir werden uns bald bei Ihnen melden", "[:site_name] New message": "[:site_name] Neue Nachricht", "Agencies": "<PERSON><PERSON>", " Publish ": " Veröffentlichen ", " Move to Draft ": " Als Entwurf speichern ", " Delete ": " Löschen ", "agent": "Agent", "All Agency": "Alle Agenturen", "Add Agency": "Agentur hinzufü<PERSON>", "Agencies Settings": "Agenturen-Einstellungen", "Agent Settings": "Agent-Einstellungen", "Edit: ": "Bearbeiten: ", "Add new agency": "Neue Agentur hinzufügen", "Permalink": "Permalink", "View agency": "Agentur anzeigen", "Save Changes": "Änderungen speichern", "Author Setting": "Autor-Einstellung", "-- Select User --": "-- <PERSON><PERSON><PERSON> auswählen --", "Feature Image": "Hauptbild", "Agency Content": "Agentur-Inhalt", "Agency name": "Agentur-Name", "Content": "Inhalt", "Office": "<PERSON><PERSON><PERSON>", "Mobile": "Mobil", "Fax": "Fax", "Banner Image": "Banner-Bild", "List Agent": "Agent auflisten", "User": "<PERSON><PERSON><PERSON>", "-- Select Agent --": "-- Agent <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> --", "Add item": "Element hinzufügen", "Locations": "<PERSON><PERSON><PERSON>", "Location": "<PERSON><PERSON>", "-- Please Select --": "-- <PERSON>te auswählen --", "Real address": "<PERSON><PERSON><PERSON> Adress<PERSON>", "The geographic coordinate": "Die geografische Koordinate", "Search by name...": "Nach Name suchen...", "Map Latitude": "Karten-Breitengrad", "Map Longitude": "Karten-Längengrad", "Map Zoom": "Karten-Zoom", "Social Info": "Social-Media-Info", "Name social": "Social-Media-Name", "Code icon": "Icon-Code", "Link social": "Social-Media-Link", "Agencies list": "Agenturen-<PERSON><PERSON>", " Bulk Actions ": " Massenaktionen ", "Do you want to delete?": "Möchten Sie löschen?", "Apply": "<PERSON><PERSON><PERSON>", "Search by name": "Nach Name suchen", "Search": "<PERSON><PERSON>", "Found :total items": ":total Elemente gefunden", "Author": "Autor", "Status": "Status", "Reviews": "Bewertungen", "Date": "Datum", "[Author Deleted]": "[<PERSON><PERSON>]", "Edit": "<PERSON><PERSON><PERSON>", "No data": "<PERSON><PERSON>", "Page Search": "Seitensuche", "Config page search of your website": "Seitensuche Ihrer Website konfigurieren", "General Options": "Allgemeine Optionen", "Title Page": "Titel-Seite", "Review Options": "Bewertungsoptionen", "Config review for agency": "Bewertung für Agentur konfigurieren", "Enable review system for Agency?": "Bewertungssystem für Agentur aktivieren?", "Yes, please enable it": "<PERSON><PERSON>, bitte aktivieren", "Turn on the mode for reviewing agency": "Modus für Agentur-Bewertung aktivieren", "Review must be approval by admin": "Bewertung muss vom Admin genehm<PERSON>t werden", "Yes please": "<PERSON><PERSON> bitte", "ON: Review must be approved by admin - OFF: Review is automatically approved": "AN: Bewertung muss vom Admin genehmigt werden - AUS: Bewertung wird automatisch genehmigt", "Review number per page": "Anzahl Bewertungen pro Seite", "Break comments into pages": "Kommentare auf Seiten aufteilen", "Agent Register": "Agent-Regis<PERSON><PERSON><PERSON>", "Agent Auto Approved?": "Agent automatisch genehmigt?", "Agent Role": "Agent-<PERSON><PERSON>", "You can edit on main lang.": "<PERSON><PERSON> können in der Hauptsprache bearbeiten.", "Agent Profile": "Agent-Profil", "Show agent email in profile?": "Agent-E-Mail im Profil anzeigen?", "Show agent phone in profile?": "Agent-Telefon im Profil anzeigen?", "Content Email Agent Registered": "E-Mail-Inhalt Agent registriert", "Content email send to Agent or Administrator when user registered.": "E-Mail-Inhalt an Agent oder Administrator senden, wenn Benutzer registriert wird.", "Enable send email to customer when customer registered ?": "E-Mail an Kunden senden, wenn Kunde registriert wird?", "You must enable on main lang.": "<PERSON><PERSON> müssen in der Hauptsprache aktivieren.", "Email to agent subject": "E-Mail an Agent Betreff", "Email to agent content": "E-Mail an Agent Inhalt", "Enable send email to Administrator when customer registered ?": "E-Mail an Administrator senden, wenn Kunde registriert wird?", "Email to Administrator subject": "E-Mail an Administrator <PERSON><PERSON><PERSON>", "Email to Administrator content": "E-Mail an Administrator Inhalt", "Config review for agent": "Bewertung für Agent konfigurieren", "Enable review system for Agent?": "Bewertungssystem für Agent aktivieren?", "Turn on the mode for reviewing agent": "Modus für Agent-Bewertung aktivieren", "Hello Administrator": "Hallo Administrator", "Here are new contact information:": "Hier sind neue Kontaktinformationen:", "Message": "Nachricht", "Password is not correct": "Passwort ist nicht korrekt", "You are not allowed to register": "Sie dürfen sich nicht registrieren", "The terms and conditions field is required": "Das Feld für Geschäftsbedingungen ist erforderlich", "Register successfully": "Erfolgreich registriert", "The email field is required": "Das E-Mail-Feld ist erforderlich", "Update successfully": "Erfolgreich aktualisiert", "Successfully logged out": "Erfolgreich abgemeldet", "Current password is not correct": "Aktuelles Passwort ist nicht korrekt", "Password updated. Please re-login": "Passwort aktualisiert. Bitte melden Sie sich erneut an", "Booking not found!": "Buchung nicht gefunden!", "You do not have permission to access": "Sie haben keine Zugriffsberechtigung", "Booking Details": "Buchungsdetails", "Location ID is not available": "Standort-ID ist nicht verfügbar", "Location not found": "Standort nicht gefunden", "News not found": "Nachrichten nicht gefunden", "You have to login in to do this": "<PERSON><PERSON> müssen sich anmelden, um dies zu tun", "Type is required": "<PERSON>p ist erforderlich", "Type does not exists": "<PERSON><PERSON> exist<PERSON>t nicht", "Resource is not available": "Ressource ist nicht verfügbar", "Resource ID is not available": "Ressourcen-ID ist nicht verfügbar", "Resource not found": "Ressource nicht gefunden", "Boat ID is not available": "Boot-ID ist nicht verfügbar", "Mobile App Settings": "Mobile App-Einstellungen", "Mobile Layout": "Mobile Layout", "Choose Layout for Mobile app": "Layout für Mobile App wählen", "Boat": "Boot", "Attributes": "Attribute", "Attributes not found!": "Attribute nicht gefunden!", "Attribute: :name": "Attribut: :name", "Attribute saved": "Attribut g<PERSON><PERSON><PERSON>", "Select at least 1 item!": "Wählen Sie mindestens 1 Element!", "Select an Action!": "W<PERSON>hlen Sie eine Aktion!", "Updated success!": "Erfolgreich aktualisiert!", "Term not found": "Begriff nicht gefunden", "Term: :name": "Begriff: :name", "Term saved": "Begriff g<PERSON>", "Boats": "<PERSON><PERSON>", "Boat Management": "Boot-Verwaltung", "Recovery": "Wiederherstellung", "Recovery Boat Management": "Boot-Verwaltung wiederherstellen", "Add Boat": "<PERSON>ot hinzufü<PERSON>", "Add new Boat": "Neues Boot hinzufügen", "Edit Boat": "<PERSON>ot bearbeiten", "Edit: :name": "Bearbeiten: :name", "DEMO MODE: can not add data": "DEMO-MODUS: Daten können nicht hinzugefügt werden", "Boat updated": "<PERSON><PERSON> aktualisi<PERSON>", "Boat created": "<PERSON>ot erstellt", "Deleted success!": "Erfolgreich gelöscht!", "Permanently delete success!": "Dauerhaft erfolgreich gelöscht!", "Recovery success!": "Wiederherstellung erfolgreich!", "Clone success!": "Klonen erfolgreich!", "Style Background": "Stil-<PERSON><PERSON>grund", "Normal": "Normal", "Slider Boatousel": "Slider<PERSON><PERSON><PERSON><PERSON>", "- Layout Normal: Background Image Uploader": "- Layout Normal: Hi<PERSON>grundbild-Uploader", "- Layout Slider: List Item(s)": "- Layout Slider: Listenelement(e)", "Background Image Uploader": "Hintergrundbild-Uploader", "Service Boat": "Service Boot", "Boat: Form Search": "Boot: Suchformular", "Desc": "Beschreibung", "Number Item": "<PERSON><PERSON><PERSON>", "Style": "Stil", "Slider Carousel": "Slider<PERSON><PERSON><PERSON><PERSON>", "Filter by Location": "<PERSON><PERSON> filtern", "Order": "Reihenfolge", "Date Create": "Erstellungsdatum", "Order By": "Sortieren nach", "ASC": "Aufsteigend", "DESC": "Absteigend", "Only featured items?": "Nur hervorgehobene Elemente?", "List by IDs": "Nach IDs auflisten", "Boat: List Items": "Boot: Elemente auflisten", "Normal Layout": "Normales Layout", "Map Layout": "<PERSON><PERSON>-Layout", "Availability": "Verfügbarkeit", "Boats Availability": "Boot-Verfügbarkeit", "Boat not found": "Boot nicht gefunden", "per Hour: ": "pro Stunde: ", "per Day: ": "pro Tag: ", "Hour: ": "Stunde: ", "Day: ": "Tag: ", "Full Book": "Vollständig gebucht", "You need to return the boat on the same-day": "Sie müssen das Boot am selben Tag zurückgeben", "This boat is not available at selected dates": "<PERSON><PERSON> ist an den ausgewählten Terminen nicht verfügbar", "Update Success": "Aktualisierung erfolgreich", ":count boats found": ":count <PERSON><PERSON> gefunden", ":count boat found": ":count <PERSON><PERSON> gefunden", "Showing :from - :to of :total Boats": "Zeige :from - :to von :total Booten", "Manage Boats": "<PERSON><PERSON> verwalten", "Recovery Boats": "<PERSON><PERSON> wiederherstellen", "Restore boat success!": "Boot erfolgreich wiederhergestellt!", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Create Boats": "<PERSON><PERSON> erstellen", "Boat not found!": "Boot nicht gefunden!", "Edit Boats": "Boote bearbeiten", "Delete boat success!": "Boot erfolgreich gelöscht!", "No item!": "Kein <PERSON>!", "Not Found": "Nicht gefunden", "Update success": "Aktualisierung erfolgreich", "Update fail!": "Aktualisierung fehlgeschlagen!", "Search for Boats": "<PERSON>ch Booten suchen", "Can not check availability": "Verfügbarkeit kann nicht überprüft werden", "Your selected dates are not valid": "Ihre ausgewählten Termine sind nicht gültig", "You must book the service for :number days in advance": "Sie müssen den Service :number Tage im Voraus buchen", "You haven't selected return day or hours": "Sie haben keinen Rückgabetag oder -stunden ausgewählt", "Start time booking: :time": "Buchungsstart: :time", "End time booking: :time": "Buchungsende: :time", "You need return boat on same-day": "Sie müssen das Boot am selben Tag zurückgeben", "Please select": "Bitte auswählen", "day": "Tag", "hour": "Stunde", "guest": "Gas<PERSON>", "Not rated": "<PERSON>cht bewertet", ":number Boats": ":number Boote", ":number Boat": ":number Boot", "Filter Price": "<PERSON><PERSON> filtern", "Review Score": "Bewertungspunktzahl", "All Boats": "<PERSON><PERSON> Boote", "Manage Boat": "<PERSON><PERSON> verwalten", "Boat Settings": "Boot-Einstellungen", "Add new attribute": "Neues Attribut hinzufügen", "Attribute Content": "Attribut-Inhalt", "Save Change": "Änderung speichern", "Attribute name": "Attribut-Name", "Position Order": "Positionsreihenfolge", "Ex: 1": "Bsp.: 1", "The position will be used to order in the Filter page search. The greater number is priority": "Die Position wird zur Sortierung in der Filter-Seitensuche verwendet. Die höhere Zahl hat Priorität", "Display Type in detail service": "Anzeigetyp im Detail-Service", "Display Left Icon": "Linkes Icon anzeigen", "Display Center Icon": "<PERSON><PERSON>res Icon anzeigen", "Hide in detail service": "<PERSON>m <PERSON>-Service ausblenden", "Enable hide": "Ausblenden aktivieren", "Hide in filter search": "In Filtersuche ausblenden", "Boat Attributes": "Boot-Attribute", "Add Attributes": "Attribute hinzufügen", "Add new": "<PERSON><PERSON>", " Bulk Action ": " Massenaktion ", "All Attributes": "Alle Attribute", "Actions": "Aktionen", "Manage Terms": "<PERSON>g<PERSON><PERSON> verwalten", "Boats Availability Calendar": "Boot-Verfügbarkeitskalender", "Showing :from - :to of :total boats": "Zeige :from - :to von :total Booten", "No boats found": "<PERSON><PERSON> gefunden", "Date Information": "Datumsinformationen", "Date Ranges": "Datumsbereiche", "Available for booking?": "Für Buchung verfügbar?", "Price per hour": "Preis pro Stunde", "Price per day": "Preis pro Tag", "Close": "Schließen", "Save changes": "Änderungen speichern", "Today": "<PERSON><PERSON>", "Boat Content": "Boot-Inhalt", "Youtube Video": "YouTube-Video", "Youtube link video": "YouTube-Video-Link", "FAQs": "Häufig gestellte Fragen", "Eg: When and where does the tour end?": "Z.B.: Wan<PERSON> und wo endet die Tour?", "Eg: Can I bring my pet?": "Z.B.: Kann ich mein Haustier mitbringen?", "Gallery": "Galerie", "Extra Info": "Zusätzliche Informationen", "Guest": "Gas<PERSON>", "Example: 3": "Beispiel: 3", "Cabin": "<PERSON><PERSON>", "Example: 5": "Beispiel: 5", "Length": "<PERSON><PERSON><PERSON>", "Example: 30m": "Beispiel: 30m", "Speed": "Geschwindigkeit", "Example: 25km/h": "Beispiel: 25km/h", "Specs": "Spezifikationen", "Eg: Range": "Z.B.: Reichweite", "Eg: 6000km": "Z.B.: 6000km", "Cancellation Policy": "Stornierungsrichtlinie", "Full refund up to 4 days prior.": "Vollständige Rückerstattung bis 4 Tage im Voraus.", "Additional Terms & Information": "Zusätzliche Bedingungen & Informationen", "For Sanitary purposes ONLY, although there is a working toilet and shower, we've deactivated the shower and the toliet is for limited use (urine only..pardon the graphic detail!)...": "Nur für sanitäre Z<PERSON>cke, obwohl es eine funktionierende Toilette und Dusche gibt, haben wir die Dusche deaktiviert und die Toilette ist nur begrenzt nutzbar (nur Urin... entschuldigen Sie die grafischen Details!)...", "Include": "Inbegriffen", "Eg: Specialized bilingual guide": "Z.B.: Spezialisierter zweisprachiger Führer", "Exclude": "Ausgeschlossen", "Eg: Additional Services": "Z.B.: Zusätzliche Services", "Loading...": "Lädt...", "Pricing": "Preisgestaltung", "Minimum advance reservations": "Mindest-Vorabreservierungen", "Ex: 3": "Bsp.: 3", "Leave blank if you dont need to use the min day option": "<PERSON><PERSON>, wenn <PERSON>e die Mindest-Tage-Option nicht benötigen", "Start time booking": "Buchungsstart", "End time booking": "Buchungsende", "*Leave it blank if don't use these fields. The end-time must be larger than start-time": "*<PERSON><PERSON>, wenn <PERSON>e diese Felder nicht verwenden. Die Endzeit muss größer als die Startzeit sein", "Enable extra price": "Zusatzpreis aktivieren", "Extra Price": "Zusatzpreis", "Price": "Pre<PERSON>", "Extra price name": "Zusatzpreis-Name", "One-time": "Ein<PERSON>ig", "Service fee": "Servicegebühr", "Enable service fee": "Servicegebühr aktivieren", "Buyer Fees": "Käufergebühren", "Fee name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fee desc": "Gebührenbeschreibung", "Fixed": "Fest", "Percent": "Prozent", "Price per person": "Preis pro Person", "Add new boat": "Neues Boot hinzufügen", "View Boat": "<PERSON><PERSON> anzeigen", "Boat Featured": "Boot hervorgehoben", "Enable featured": "Hervorhebung aktivieren", "Is Instant Booking?": "Ist Sofortbu<PERSON>ng?", "Enable instant booking": "Sofortbuchung aktivieren", "Default State": "Standardzustand", "-- Please select --": "-- <PERSON>te auswählen --", "Always available": "Immer verfügbar", "Only available on specific dates": "<PERSON>ur an bestimmten Terminen verfügbar", " Recovery ": " Wiederherstellung ", "Permanently delete": "Dauerhaft löschen", "Move to Pending": "<PERSON><PERSON> verschieben", " Clone ": " Klonen ", "Advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Featured": "Hervorgehoben", "No boat found": "<PERSON><PERSON> gefunden", "Banner Page": "Banner-Seite", "Layout Search": "Layout-<PERSON><PERSON>", "Location Search Style": "Standort-Suchstil", "Autocomplete from locations": "Autovervollständi<PERSON><PERSON> von <PERSON>", "Autocomplete from Gmap Place": "Autovervollständigung von Google Maps-Orten", "Limit item per Page": "Elemente pro Seite begrenzen", "Default: 9": "Standard: 9", "Radius options": "Radius-Optionen", "Miles": "<PERSON><PERSON>", "Km": "Km", "Layout Map Option": "<PERSON>rten-Layout-Option", "Map Left": "Karte links", "Map Right": "<PERSON><PERSON> rechts", "Map Lat Default": "Karten-Breitengrad Standard", "Map Lng Default": "Karten-Längengrad Standard", "Map Zoom Default": "Karten-Zoom Standard", "Get lat - lng in here": "Breitengrad - Längengrad hier abrufen", "Icon Marker in Map": "Icon-Markierung in Karte", "SEO Options": "SEO-Optionen", "Share Facebook": "Facebook teilen", "Share Twitter": "Twitter teilen", "Seo Title": "SEO-Titel", "Enter title...": "Titel eingeben...", "Seo Description": "SEO-Beschreibung", "Enter description...": "Beschreibung eingeben...", "Featured Image": "Hauptbild", "Facebook Title": "Facebook-Titel", "Facebook Description": "Facebook-Beschreibung", "Facebook Image": "Facebook-Bild", "Twitter Title": "Twitter-Titel", "Twitter Description": "Twitter-Beschreibung", "Twitter Image": "Twitter-Bild", "Config review for boat": "Bewertung für Boot konfigurieren", "Enable review system for Boat?": "Bewertungssystem für Boot aktivieren?", "Turn on the mode for reviewing boat": "Modus für Boot-Bewertung aktivieren", "Customer must book a boat before writing a review?": "Kunde muss <PERSON> buchen, bevor er eine Bewertung schreibt?", "ON: Only post a review after booking - Off: Post review without booking": "AN: Bewertung nur nach Buchung - AUS: Bewertung ohne Buchung", "Allow review after making Completed Booking?": "Bewertung nach abgeschlossener Buchung erlauben?", "Pick to the Booking Status, that allows reviews after booking": "Buchungsstatus wählen, der Bewertungen nach Buchung erlaubt", "Leave blank if you allow writing the review with all booking status": "<PERSON><PERSON>, wenn Sie Bewertungen bei allen Buchungsstatus erlauben", "Review criteria": "Bewertungskriterien", "Eg: Service": "Z.B.: Service", "Booking Buyer Fees Options": "Buchungs-Käufergebühren-Optionen", "Config buyer fees for boat": "Käufergebühren für Boot konfigurieren", "Vendor Options": "Anbieter-Optionen", "Vendor config for boat": "Anbieter-Konfiguration für Boot", "Boat created by vendor must be approved by admin": "<PERSON> erstelltes Boot muss vom Admin genehmigt werden", "ON: When vendor posts a service, it needs to be approved by administrator": "AN: <PERSON><PERSON> einen Service postet, muss er vom Administrator gene<PERSON><PERSON><PERSON> werden", "Allow vendor can change their booking status": "Anbieter darf Buchungsstatus ändern", "ON: Vendor can change their booking status": "AN: <PERSON><PERSON><PERSON> kann Buchungsstatus ändern", "Allow vendor can change their booking paid amount": "An<PERSON><PERSON> darf bezahlten Buchungsbetrag ändern", "ON: Vendor can change their booking paid amount": "AN: <PERSON><PERSON><PERSON> kann bezahlten Buchungsbetrag ändern", "Allow vendor can add service fee": "Anbieter darf Servicegebühr hinzufügen", "ON: Vendor can add service fee": "AN: <PERSON><PERSON><PERSON> kann Servicegebühr hinzufügen", "Booking Deposit": "Buchungskaution", "Booking Deposit Options": "Buchungskautions-Optionen", "Deposit Amount": "Kautionsbetrag", "Deposit Fomular": "Kautionsformel", "Default": "Standard", "Deposit amount + Buyer free": "Kautionsbetrag + Käufer frei", "Disable boat module?": "Boot-<PERSON><PERSON><PERSON>?", "Disable boat module": "Boot<PERSON><PERSON><PERSON><PERSON>", "Yes, please disable it": "<PERSON><PERSON>, bitte deaktivieren", "Form Search Fields": "Suchformular-Felder", "Search Criteria": "Suchkriterien", "Search Field": "<PERSON><PERSON>", "Service name": "Service-Name", "Attribute": "Attribut", "-- Select field type --": "-- <PERSON>ldtyp auswählen --", "-- Select Attribute --": "-- Attribut auswählen --", "Size Column 6": "Spaltengröße 6", "Size Column 4": "Spaltengröße 4", "Size Column 3": "Spaltengröße 3", "Size Column 2": "Spaltengröße 2", "Size Column 1": "Spaltengröße 1", "Map Search Fields": "<PERSON><PERSON><PERSON><PERSON>", "Advance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add new term": "Neuen Begriff hinzufügen", "Term Content": "Begriff-Inhalt", "Term name": "Begriff-Name", "Class Icon": "Klassen-Icon", "get icon in <a href=':link_1' target='_blank'>fontawesome.com</a> or <a href=':link_2' target='_blank'>icofont.com</a>": "Icon abrufen unter <a href=':link_1' target='_blank'>fontawesome.com</a> oder <a href=':link_2' target='_blank'>icofont.com</a>", "Ex: fa fa-facebook": "Bsp.: fa fa-facebook", "Upload image size 30px": "Bild hochladen Größe 30px", "All the Term's image are same size": "Alle Begriff-Bilder haben die gleiche Größe", "Add Term": "Begriff hinzufügen", "All Terms": "Alle Begriffe", "Boat information": "Boot-Informationen", "Booking Number": "Buchungsnummer", "Booking Status": "Buchungsstatus", "Payment method": "Zahlungsmethode", "Payment Note": "Zahlungsnotiz", "Boat name": "Boot-Name", "Address": "<PERSON><PERSON><PERSON>", "Start date": "Startdatum", "End date:": "Enddatum:", "Durations:": "Dauer:", "Rental price:": "Mietpreis:", "Extra Prices:": "Zusatzpreise:", "Coupon": "Gutschein", "Total": "Gesamt", "Remain": "Verbleibend", "Manage Bookings": "Buchungen verwalten", "Checkout": "<PERSON><PERSON>", "You have to verify email first": "Sie müssen zuerst Ihre E-Mail verifizieren", "Service not found": "Service nicht gefunden", "Please verify the captcha": "Bitte verifizieren Sie das Captcha", "The password confirmation does not match": "Die Passwort-Bestätigung stimmt nicht überein", "The password must be at least 6 characters": "Das Passwort muss mindestens 6 Zeichen haben", "Your credit balance is :amount": "<PERSON>hr Guthaben beträgt :amount", "Term conditions is required field": "Geschäftsbedingungen sind ein Pflichtfeld", "Payment gateway is required field": "Zahlungsgateway ist ein Pflichtfeld", "Payment gateway not found": "Zahlungsgateway nicht gefunden", "Payment gateway is not available": "Zahlungsgateway ist nicht verfügbar", "You payment has been processed successfully": "Ihre Zahlung wurde erfolgreich verarbeitet", "Service type not found": "Service-<PERSON>p nicht gefunden", "Service is not bookable": "Service ist nicht buchbar", "You cannot book your own service": "Sie können Ihren eigenen Service nicht buchen", "Thank you for contacting us! We will be in contact shortly.": "Vielen Dank für Ihre Kontaktaufnahme! Wir werden uns in Kürze bei Ihnen melden.", "Remain can not smaller than 0": "Verbleibend kann nicht kleiner als 0 sein", "Booking not found": "Buchung nicht gefunden", "You don't have access.": "<PERSON>e haben keinen Zugriff.", "You booking has been changed successfully": "Ihre Buchung wurde erfolgreich geändert", "You got reply from :name": "Sie haben eine Antwort von :name erhalten", "[:site_name] New inquiry has been made": "[:site_name] Neue Anfrage wurde gestellt", "[:site_name] You get new inquiry request": "[:site_name] Sie haben eine neue Anfrage erhalten", "[:site_name] New booking has been made": "[:site_name] Neue Buchung wurde erstellt", "[:site_name] Your service got new booking": "[:site_name] Ihr Service hat eine neue Buchung erhalten", "Thank you for booking with us": "Vielen Dank für Ihre Buchung bei uns", "[:site_name] The booking status has been updated": "[:site_name] Der Buchungsstatus wurde aktualisiert", "Your booking status has been updated": "Ihr Buchungsstatus wurde aktualisiert", "Your payment has been canceled": "Ihre Zahlung wurde storniert", "Thank you, we will contact you shortly": "<PERSON><PERSON>en Dank, wir werden uns in Kürze bei Ihnen melden", "Enable Offline Payment?": "Offline-Zahlung aktivieren?", "Custom Name": "Benutzerdefinierter Name", "Offline Payment": "Offline-Zahlung", "Custom Logo": "Benutzerdefiniertes Logo", "Custom HTML Description": "Benutzerdefinierte HTML-Beschreibung", "Enable Paypal Standard?": "PayPal Standard aktivieren?", "Paypal": "PayPal", "Enable Sandbox Mod?": "Sandbox-Modus aktivieren?", "Convert To": "Umwandeln in", "In case of main currency does not support by PayPal. You must select currency and input exchange_rate to currency that PayPal support": "Falls die Hauptwährung von PayPal nicht unterstützt wird. Sie müssen eine Währung auswählen und den Wechselkurs zu einer von PayPal unterstützten Währung eingeben", "Exchange Rate": "Wechselkurs", "Example: Main currency is VND (which does not support by PayPal), you may want to convert it to USD when customer checkout, so the exchange rate must be 23400 (1 USD ~ 23400 VND)": "Beispiel: Hauptwährung ist VND (die von PayPal nicht unterstützt wird), <PERSON><PERSON> möchten sie beim Checkout in USD umwandeln, daher muss der Wechselkurs 23400 betragen (1 USD ~ 23400 VND)", "Sandbox API Username": "Sandbox API-Benutzername", "Sandbox API Password": "Sandbox API-Passwort", "Sandbox Signature": "Sandbox-Signatur", "API Username": "API-Benutzername", "API Password": "API-Passwort", "Signature": "Signatur", "Booking status does need to be paid": "Buchungsstatus muss bezahlt werden", "Booking total is zero. Can not process payment gateway!": "Buchungsgesamtbetrag ist null. Zahlungsgateway kann nicht verarbeitet werden!", "Payment Failed": "Zahlung fehlgeschlagen", "You cancelled the payment": "Sie haben die Zahlung storniert", "PayPal does not support currency: :name": "PayPal unterstützt die Währung nicht: :name", "Exchange rate to :name must be specific. Please contact site owner": "Wechsel<PERSON><PERSON> zu :name muss spezifisch sein. Bitte kontaktieren Sie den Website-Besitzer", "Enable Payrexx Checkout?": "Payrexx Checkout aktivieren?", "Payrexx Checkout": "Payrexx Checkout", "Instance name": "Instanz-Name", "Api secret key": "API-Geheimschlüssel", "Url callback: ": "URL-Callback: ", "Your payment has been placed": "Ihre Zahlung wurde platziert", "Payment Processing": "Zahlungsverarbeitung", "Payment Failed.": "Zahlung fehlgeschlagen.", "You payment has been processed successfully before": "Ihre Zahlung wurde bereits erfolgreich verarbeitet", "No information found": "<PERSON>ine Informationen gefunden", "referenceId can't null": "Referenz-<PERSON> kann nicht null sein", "Enable Paystack gateway?": "Paystack-Gateway aktivieren?", "Paystack": "Paystack", "Public key": "Öffentlicher Schlüssel", "Secret key": "Geheimer Schlüssel", "Payment Url": "Zahlungs-URL", "Merchant Email": "Händler-E-Mail", "not update status \" . $response['event'])]);\r\n                    }\r\n                    else {\r\n                        return response()->json(['status' => 'error": "Status nicht aktualisiert \" . $response['event'])]);\r\n                    }\r\n                    else {\r\n                        return response()->json(['status' => 'error", "Enable Stripe Checkout V2?": "Stripe Checkout V2 aktivieren?", "Stripe": "Stripe", "Secret Key": "Geheimer Schlüssel", "Publishable Key": "Veröffentlich<PERSON><PERSON>", "Enable Sandbox Mode": "Sandbox-Modus aktivieren", "Test Secret Key": "Test-Geheimschlüssel", "Test Publishable Key": "Test-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Webhook Secret": "Webhook-Geheimnis", "Webhook url: <code>:code</code>": "Webhook-URL: <code>:code</code>", "Webhook error while validating signature.": "Webhook-Fehler beim Validieren der Signatur.", "Payment not found": "Zahlung nicht gefunden", "Received unknown event type": "Unbekannter Ereignistyp erhalten", "Buy credits": "<PERSON><PERSON><PERSON><PERSON> kaufen", ":name has created new Booking": ":name hat eine neue Buchung erstellt", ":name has changed to :status": ":name wurde zu :status geändert", ":name has sent a Enquiry for :title": ":name hat eine Anfrage für :title gesendet", ":name has updated the PAID amount on :title": ":name hat den BEZAHLTEN Betrag für :title aktualisiert", "Administrator has updated the PAID amount on :title": "Administrator hat den BEZAHLTEN Betrag für :title aktualisiert", "Revenue": "Umsatz", "Total revenue": "Gesamtumsatz", "Earning": "<PERSON><PERSON><PERSON>", "Total Earning": "Gesamtverdienst", "Bookings": "Buchungen", "Total bookings": "Gesamtbuchungen", "Services": "Services", "Total bookable services": "Gesamte buchbare Services", "Total Revenue": "Gesamtumsatz", "Total pending": "Gesamt ausstehend", "Earnings": "Verdienste", "Total earnings": "Gesamtverdienste", "Total Pending": "Gesamt ausstehend", "Total Fees": "Gesamtgebühren", "Total Commission": "Gesamtprovision", "Total Booking": "Gesamtbuchung", "Payment fail": "Zahlung fehlgeschlagen", "Transaction not found": "Transaktion nicht gefunden", "Payment updated": "Zahlung aktualisiert", "Plan fail": "Plan fehlgeschlagen", "Plan updated": "Plan aktualisiert", "Booking Settings": "Buchungseinstellungen", "Payment Settings": "Zahlungseinstellungen", "Enquiry Settings": "Anfrage-Einstellungen", "Guest Checkout": "Gast-Checkout", "Enable guest checkout": "Gast-Checkout aktivieren", "Yes, please": "<PERSON>a, bitte", "Enable Ticket/Guest information": "Ticket-/Gast-Informationen aktivieren", "Checkout Page": "Checkout-Seite", "Change your checkout page options": "Ändern Sie Ihre Checkout-Seitenoptionen", "Enable reCapcha Booking Form": "reCaptcha für Buchungsformular aktivieren", "On ReCapcha": "reCaptcha an", "Turn on the mode for booking form": "Modus für Buchungsformular aktivieren", "Terms & Conditions page": "Geschäftsbedingungen-Seite", "Invoice Page": "Rechnungsseite", "Change your invoice page options": "Ändern Sie Ihre Rechnungsseitenoptionen", "Invoice Logo": "Rechnungslogo", "Invoice Company Info": "Rechnungs-Firmeninfo", "Settings Enquiry for Service": "Anfrage-Einstellungen für Service", "Change your enquiry options": "Ändern Sie Ihre Anfrage-Optionen", "Enable enquiry for Hotel": "Anfrage für Hotel aktivieren", "Enable enquiry form": "Anfrageformular aktivieren", "Enquiry Type": "Anfrage-Typ", "Booking & Enquiry": "Buchung & Anfrage", "Only Enquiry": "<PERSON><PERSON> Anfrage", "Enable enquiry for Tour": "Anfrage für Tour aktivieren", "Enable enquiry for Space": "Anfrage für Raum aktivieren", "Enable enquiry for Car": "Anfrage für Auto aktivieren", "Enable enquiry for Event": "Anfrage für Event aktivieren", "Enable enquiry for Boat": "Anfrage für Boot aktivieren", "Settings Enquiry": "Anfrage-Einstellungen", "Enable re-catpcha for enquiry?": "reCaptcha für Anfrage aktivieren?", "Enable re-captcha at enquiry form": "reCaptcha im Anfrageformular aktivieren", "Settings Email Enquiry": "E-Mail-Anfrage-Einstellungen", "Change your email enquiry options": "Ändern Sie Ihre E-Mail-Anfrage-Optionen", "Enable send email to Vendor": "E-Mail an Anbieter senden aktivieren", "Email to Vendor content": "E-Mail an Anbieter Inhalt", "Enable send email to Administrator": "E-Mail an Administrator senden aktivieren", "Currency": "Währung", "Currency Format": "Währungsformat", "Main Currency": "Hauptwährung", "Format": "Format", "Right (100$)": "Rechts (100$)", "Right with space (100 $)": "Re<PERSON>s mit Leerzeichen (100 $)", "Left ($100)": "Links ($100)", "Left with space ($ 100)": "<PERSON><PERSON> mit Leerzeichen ($ 100)", "Thousand Separator": "Tausendertrennzeichen", "Decimal Separator": "Dezimaltrennzeichen", "No. of Decimals": "<PERSON><PERSON><PERSON>", "Extra Currency": "Zusätzliche Währung", "Sub Currency": "Unterwährung", "Exchange rate": "Wechselkurs", "Example: Main currency is VND, and the extra currency is USD, so the exchange rate must be 23400 (1 USD ~ 23400 VND)": "Beispiel: Hauptwährung ist VND und die zusätzliche Währung ist USD, daher muss der Wechselkurs 23400 betragen (1 USD ~ 23400 VND)", "Payment Gateways": "Zahlungsgateways", "You can enable and disable your payment gateways here": "Sie können hier Ihre Zahlungsgateways aktivieren und deaktivieren", "Your Information": "<PERSON><PERSON>e <PERSON>en", "First name": "<PERSON><PERSON><PERSON>", "Last name": "Nachname", "Address line 1": "Adresszeile 1", "Address line 2": "Adresszeile 2", "City": "Stadt", "State/Province/Region": "Bundesland/Provinz/Region", "ZIP code/Postal code": "PLZ/Postleitzahl", "Country": "Land", "Special Requirements": "Besondere Anforderungen", "Credit want to pay?": "<PERSON><PERSON> be<PERSON>?", "Credit": "<PERSON><PERSON><PERSON><PERSON>", "Pay now": "Jetzt bezahlen", "How do you want to pay?": "Wie möchten Sie bezahlen?", "Pay deposit": "<PERSON><PERSON> be<PERSON>en", "Pay in full": "Vollständig bezahlen", "Create a new account?": "Neues Konto erstellen?", "First Name": "<PERSON><PERSON><PERSON>", "Last Name": "Nachname", "<EMAIL>": "<EMAIL>", "Your Phone": "Ihr Telefon", "Password": "Passwort", "Password confirmation": "Passwort-Bestätigung", "Your City": "<PERSON><PERSON><PERSON> St<PERSON>t", "I have read and accept the": "Ich habe gelesen und akzeptiere die", "terms and conditions": "Geschäftsbedingungen", "Submit": "<PERSON><PERSON><PERSON><PERSON>", "Select Payment Method": "Zahlungsmethode auswählen", "Booking Submission": "Buchungsübermittlung", "your booking was submitted successfully!": "Ihre Buchung wurde erfolgreich übermittelt!", "Booking details has been sent to:": "Buchungsdetails wurden gesendet an:", "Booking Date": "Buchungsdatum", "Payment Method": "Zahlungsmethode", "Booking History": "Buchungsverlauf", "Name on the Card": "Name auf der <PERSON>", "Card Name": "<PERSON><PERSON><PERSON>", "Card Number": "Kartennummer", "Expiration": "Ablaufdatum", "CVC": "CVC", "Enquiry": "Anfrage", "Name *": "Name *", "Email *": "E-Mail *", "Note": "Notiz", "Send now": "Jetzt senden", "Car": "Auto", "Cars": "Autos", "Car Management": "Auto-Verwaltung", "Recovery Car Management": "Auto-Verwaltung wiederherstellen", "Add Car": "Auto hinzufügen", "Add new Car": "Neues Auto hinzufügen", "Edit Car": "Auto bearbeiten", "Car updated": "Auto aktualisiert", "Car created": "Auto erstellt", "Select term car": "Auto-Begriff auswählen", "Service Car": "Service Auto", "Car: Term Featured Box": "Auto: Begriff-Hervorhebungsbox", "Car: Form Search": "Auto: Suchformular", "Car: List Items": "Auto: Elemente auflisten", "Cars Availability": "Auto-Verfügbarkeit", "Car not found": "Auto nicht gefunden", ":count cars found": ":count Autos gefunden", ":count car found": ":count Auto gefunden", "Showing :from - :to of :total Cars": "Zeige :from - :to von :total Autos", "Manage Cars": "Autos verwalten", "Recovery Cars": "Autos wiederherstellen", "Restore car success!": "Auto erfolgreich wiederhergestellt!", "Create Cars": "Autos erstellen", "Car not found!": "Auto nicht gefunden!", "Edit Cars": "Autos bearbeiten", "Delete car success!": "Auto erfolgreich gelöscht!", "Search for Cars": "Nach Autos suchen", "This car is not available at selected dates": "Dieses Auto ist an den ausgewählten Terminen nicht verfügbar", "You must to book a minimum of :number days": "<PERSON>e müssen mindestens :number Tage buchen", "Please select date!": "Bitte Datum auswählen!", ":number Cars": ":number Autos", ":number Car": ":number Auto", "All Cars": "Alle Autos", "Manage Car": "Auto verwalten", "Car Settings": "Auto-Einstellungen", "Car Attributes": "Auto-Attribute", "Cars Availability Calendar": "Auto-Verfügbarkeitskalender", "Showing :from - :to of :total cars": "Zeige :from - :to von :total Autos", "No cars found": "Keine Autos gefunden", "Instant Booking?": "Sofortbuchung?", "Car Content": "Auto-Inhalt", "Passenger": "<PERSON><PERSON><PERSON>", "Gear Shift": "Gangschaltung", "Example: Auto": "Beispiel: Automatik", "Baggage": "<PERSON><PERSON><PERSON><PERSON>", "Door": "<PERSON><PERSON><PERSON>", "Example: 4": "Beispiel: 4", "Ical": "iCal", "Import url": "Import-URL", "Export url": "Export-URL", "Car Number": "Auto-Nummer", "Car Price": "Auto-Preis", "Sale Price": "Verkaufspreis", "Car Sale Price": "Auto-Verkaufspreis", "If the regular price is less than the discount , it will show the regular price": "Wenn der reguläre Preis niedriger als der Rabatt ist, wird der reguläre Preis angezeigt", "Minimum day stay requirements": "Mindest-Aufenthaltsdauer-Anforderungen", "Ex: 2": "Bsp.: 2", "Leave blank if you dont need to set minimum day stay option": "<PERSON><PERSON>, wenn <PERSON><PERSON> die Mindest-Aufenthaltsdauer-<PERSON><PERSON> nicht benötigen", "Per day": "Pro Tag", "Add new car": "Neues Auto hinzufügen", "View Car": "Auto anzeigen", "Car Featured": "Auto hervorgehoben", "No car found": "Kein Auto gefunden", "Config review for car": "Bewertung für Auto konfigurieren", "Enable review system for Car?": "Bewertungssystem für Auto aktivieren?", "Turn on the mode for reviewing car": "Modus für Auto-Bewertung aktivieren", "Customer must book a car before writing a review?": "Kunde muss Auto buchen, bevor er eine Bewertung schreibt?", "Config buyer fees for car": "Käufergebühren für Auto konfigurieren", "Vendor config for car": "Anbieter-Konfiguration für Auto", "Car created by vendor must be approved by admin": "Von <PERSON> erstelltes Auto muss vom Admin genehmigt werden", "Disable car module?": "Auto-Mo<PERSON>l <PERSON>?", "Disable car module": "Auto-Mo<PERSON>l <PERSON>", "Car information": "Auto-Informationen", "Car name": "Auto-Name", "Days:": "Tage:", "Adults": "Erwachsene", "Children": "Kinder", ":count day": ":count Tag", ":count days": ":count <PERSON><PERSON>", "Contact Submissions": "Kontakt-Übermittlungen", "Please select at least 1 item!": "Bitte wählen Sie mindestens 1 Element!", "No Action is selected!": "Keine Aktion ausgewählt!", "Class Block": "Klassen-Block", "Other Block": "Anderer Block", "Contact Block": "Kontakt-Block", "Contact Page": "Kontakt-Seite", "Contact": "Kontakt", "All Contact Submissions": "Alle Kontakt-Übermittlungen", "Search...": "Suchen...", "SEND MESSAGE": "NACHRICHT SENDEN", "Primary": "<PERSON><PERSON><PERSON><PERSON>", "Footer": "Fußzeile", "Menus": "<PERSON><PERSON><PERSON>", "Create new menu": "Neues Menü er<PERSON>llen", "Page": "Seite", "News Category": "Nachrichten-Kategorie", "Menu not found": "<PERSON>ü nicht gefunden", "You can not edit menu in demo mode": "<PERSON><PERSON> können das Menü im Demo-Modus nicht bearbeiten", "Your menu has been saved": "Ihr Menü wurde gespeichert", "Module Management": "Modul-<PERSON>erwaltung", "All Notifications": "Alle Benachrichtigungen", "DEMO MODE: Disable setting update": "DEMO-MODUS: Einstellungsupdate deaktiviert", "Settings Saved": "Einstellungen gespeichert", "Please enter license key": "Bitte Lizenzschlüssel eingeben", "Can not connect to update server. Please check again": "Verbindung zum Update-Server nicht möglich. Bitte erneut prüfen", "You are using latest version of Booking Core": "Sie verwenden die neueste Version von Booking Core", "Can not get update file from server": "Update-<PERSON><PERSON> kann nicht vom Server abgerufen werden", "Can not download update file to folder storage": "Update-<PERSON><PERSON> kann nicht in den Speicherordner heruntergeladen werden", "Can not un-zip the package": "<PERSON>et kann nicht entpackt werden", "License information has been saved": "Lizenzinformationen wurden gespeichert", "You cant save cookie": "<PERSON>e können Cook<PERSON> nicht speichern", "Clear cache success!": "<PERSON><PERSON> erfolg<PERSON>ich geleert!", "Dashboard": "Dashboard", "Setting": "Einstellung", "installer_messages.environment.wizard.form.name_required": "Name ist erforderlich", "installer_messages.environment.wizard.form.db_connection_failed": "Datenbankverbindung fehlgeschlagen", ":name has created :services :title": ":name hat :services :title erstellt", ":name has created a Review :review on :title": ":name hat eine Bewertung :review für :title erstellt", ":title has been deleted by :by": ":title wurde von :by <PERSON><PERSON><PERSON><PERSON>", ":title was updated to :status by :by": ":title wurde von :by auf :status aktualisiert", "Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "General Settings": "Allgemeine Einstellungen", "Menu": "<PERSON><PERSON>", "Tools": "Werkzeuge", "Modules": "<PERSON><PERSON><PERSON>", "Languages": "<PERSON><PERSON><PERSON>", "Translation Manager": "Übersetzungsmanager", "System Logs": "System-Logs", "System": "System", "Advanced Settings": "Erweiterte Einstellungen", "Style Settings": "Stil-Einstellungen", "Vendor": "<PERSON><PERSON><PERSON>", "-- Vendor --": "-- <PERSON><PERSON><PERSON> --", "-- All Location --": "-- <PERSON><PERSON> --", "-- All --": "-- Alle --", "Only Featured": "<PERSON><PERSON>", "Edit Menu:": "<PERSON><PERSON> bearbeiten:", "Menu name": "Menü-Name", "No items found": "<PERSON>ine Elemente gefunden", "Add to Menu": "Zum Menü hinzufügen", "Custom Url": "Benutzerdefinierte URL", "URL": "URL", "Link Text": "Link-Text", "Menu items": "Menü-Elemente", "Label": "Bezeichnung", "Class": "Klass<PERSON>", "Target": "<PERSON><PERSON>", "Open new tab": "Neuen Tab <PERSON>", "Enable mega menu": "Mega-Menü aktivieren", "Columns": "Spalten", "2 columns": "2 Spalten", "3 columns": "3 Spalten", "4 columns": "4 Spalten", "Mega image url": "Mega-Bild-URL", "Delete": "Löschen", "Origin: ": "Ursprung: ", "Menu Configs": "Menü-Konfigurationen", "Save Menu": "<PERSON>ü s<PERSON>ichern", "Menu Management": "Menü-Verwaltung", "All Menus": "<PERSON><PERSON>", "Use for": "Verwenden für", "All Modules": "Alle Module", "Active": "Aktiv", "Deactivate": "Deaktivieren", "Module name": "Modul-Name", "Description": "Beschreibung", "Version": "Version", "No Module found": "<PERSON><PERSON> gefunden", "Unread": "<PERSON><PERSON><PERSON><PERSON>", "Read": "<PERSON><PERSON><PERSON>", "You don't have any notifications": "Sie haben keine Benachrichtigungen", "Search engine": "Suchmaschine", "Search Engine": "Suchmaschine", "Allow search engines to show this service in search results?": "Suchmas<PERSON>en erlauben, diesen Service in Suchergebnissen anzuzeigen?", "Yes": "<PERSON>a", "No": "<PERSON><PERSON>", "Leave blank to use service title": "<PERSON><PERSON>, um Service-Tite<PERSON> zu verwenden", "Search Options": "Suchoptionen", "Search open tab": "<PERSON>e in neuem Tab öffnen", "Current Tab": "Aktueller Tab", "Open New Tab": "Neuen Tab <PERSON>", "Square Size Unit": "Quadratische Größeneinheit", "Size Unit": "Größeneinheit", "Square metre (m2)": "Quadratmeter (m²)", "Square feet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map Provider": "Kartenan<PERSON><PERSON>", "Change map provider of your website": "Kartenanbieter Ihrer Website ändern", "OpenStreetMap.org": "OpenStreetMap.org", "Google Map": "Google Maps", "Gmap API Key": "Google Maps API-Schlüssel", "Learn how to get an api key": "<PERSON><PERSON><PERSON><PERSON>, wie Sie einen API-Schlüssel erhalten", "Map Options Default": "Standard-Kartenoptionen", "Map Clustering": "Karten-Clustering", "Off": "Aus", "On": "An", "Map fitBounds": "Karte an Grenzen anpassen", "Social Login": "Social Login", "Change social login information for your website": "Social Login-Informationen für Ihre Website ändern", "Facebook": "Facebook", "Enable Facebook Login?": "Facebook-Login aktivieren?", "Facebook Client Id": "Facebook Client-ID", "Facebook Client Secret": "Facebook Client-Secret", "Google": "Google", "Enable Google Login?": "Google-Login aktivieren?", "Google Client Id": "Google Client-ID", "Google Client Secret": "Google Client-Secret", "Twitter": "Twitter", "Enable Twitter Login?": "Twitter-Login aktivieren?", "Twitter Client Id": "Twitter Client-ID", "Twitter Client Secret": "Twitter Client-Secret", "Captcha": "<PERSON><PERSON>", "ReCaptcha Config": "ReCaptcha-Konfiguration", "Enable ReCaptcha": "ReCaptcha aktivieren", "Version 2": "Version 2", "Version 3": "Version 3", "Api Key": "API-Schlüssel", "Api Secret": "API-Secret", "Custom Scripts for all languages": "Benutzerdefinierte Skripte für alle Sprachen", "Add custom HTML script before and after the content, like tracking code": "Benutzerdefinierte HTML-Skripte vor und nach dem Inhalt hinzufügen, wie Tracking-Code", "Custom Scripts": "Benutzerdefinierte Skripte", "Head Script": "Head-<PERSON><PERSON><PERSON><PERSON>", "scripts before closing head tag": "Skripte vor schließendem Head-Tag", "Body Script": "Body-<PERSON><PERSON><PERSON><PERSON>", "scripts after open of body tag": "Skripte nach öffnendem Body-Tag", "Footer Script": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom Scripts for :name": "Benutzerdefinierte Skripte für :name", "Cookie agreement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Cookie agreement config": "Cookie-Vereinbarung-Konfiguration", "-- Select one --": "-- <PERSON><PERSON> ausw<PERSON>hl<PERSON> --", "Style 1": "Stil 1", "Style 2": "Stil 2", "Agree Text Button": "Zustimmungs-Text-<PERSON><PERSON>", "Site Information": "Website-Informationen", "Information of your website for customer and goole": "Informationen Ihrer Website für Kunden und Google", "Site title": "Website-Titel", "Site Desc": "Website-Beschreibung", "Date format": "Datumsformat", "Timezone": "Zeitzone", "-- Default --": "-- Standard --", "Change the first day of week for the calendars": "<PERSON><PERSON>ag für Kalender ändern", "Monday": "Montag", "Sunday": "Sonntag", "Language": "<PERSON><PERSON><PERSON>", "Change language of your websites": "Sprache Ihrer Website ändern", "Select default language": "Standardsprache auswählen", "Manage languages here": "<PERSON><PERSON><PERSON> hier verwalten", "Enable Multi Languages": "Mehrsprachigkeit aktivieren", "Enable": "Aktivieren", "Enable RTL": "RTL aktivieren", "Homepage": "Startseite", "Change your homepage content": "Inhalt Ihrer Startseite ändern", "Page for Homepage": "Seite für Startseite", "Header & Footer Settings": "Kopf- und Fußzeilen-Einstellungen", "Change your options": "Ihre Optionen ändern", "Logo": "Logo", "Favicon": "Favicon", "Topbar Left Text": "Topbar linker Text", "Footer List Widget": "Fußzeilen-Listen-Widget", "Size": "Größe", "Footer Text Left": "Fußzeilen-Text links", "Footer Text Right": "Fußzeilen-Text rechts", "Page contact settings": "Kontaktseiten-Einstellungen", "Settings for contact page": "Einstellungen für Kontaktseite", "Contact title": "Kontakt-Titel", "Contact sub title": "Kontakt-Untertitel", "Contact Desc": "Kontakt-Beschreibung", "Contact Featured Image": "Kontakt-Hauptbild", "Cookie preferences": "Cookie-Einstellungen", "Cookie Settings Modal": "Cookie-Einstellungen-Modal", "Cookie Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Primary button Text": "<PERSON><PERSON><PERSON><PERSON>-Text", "Accept all": "Alle akzeptieren", "Primary button Role": "<PERSON><PERSON><PERSON><PERSON>", "Accept selected": "Ausgewählte akzeptieren", "Secondary button Text": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Text", "Settings": "Einstellungen", "Secondary button Role": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Open modal settings": "Modal-Einstellungen öffnen", "Accept necessary": "Notwendige akzeptieren", "Button save setting text": "Button-Einstellungen-speichern-Text", "Save settings": "Einstellungen speichern", "Button Accept All text": "Button-Alle-akzeptieren-Text", "Accept All": "Alle akzeptieren", "Button Reject All text": "Button-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Text", "Reject All": "<PERSON>e <PERSON>en", "Setting options": "Einstellungsoptionen", "Action": "Aktion", "Toggle": "Umschalten", "Readonly": "<PERSON><PERSON> lesen", "Value": "Wert", "Config Broadcast": "Broadcast konfigurieren", "Change your config broadcast site": "Ihre Broadcast-Website-Konfiguration ändern", "Broadcast Driver": "Broadcast-<PERSON><PERSON><PERSON>", "Pusher API": "Pusher API", "Change your API for pusher here. It will use for chat plugin and notification": "Ändern Sie hier Ihre API für Pusher. Sie wird für Chat-Plugin und Benachrichtigungen verwendet", "Pusher API Information": "Pusher API-Informationen", "API KEY": "API-SCHLÜSSEL", "API Secret": "API-Secret", "APP ID": "APP-ID", "Cluster": "Cluster", "General Style": "Allgemeiner Stil", "Change main color, typo ...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Typografie ändern ...", "Main color": "Hauptfarbe", "Typography": "<PERSON><PERSON><PERSON><PERSON>", "Font Family": "Schriftfamilie", "Color": "Farbe", "Font Size": "Schriftgröße", "Line Height": "Zeilenhöhe", "Font Weight": "Schriftstärke", "bold or 400": "fett oder 400", "H1,H2,H3 Options": "H1,H2,H3-<PERSON><PERSON><PERSON>", "H1 Font Family": "H1-Schriftfamilie", "H2 Font Family": "H2-Schriftfamilie", "H3 Font Family": "H3-Schriftfamilie", "Custom CSS for all languages": "Benutzerdefiniertes CSS für alle Sprachen", "Write your own custom css code": "Schreiben Sie Ihren eigenen benutzerdefinierten CSS-Code", "Custom CSS": "Benutzerdefiniertes CSS", "Custom CSS for :name": "Benutzerdefiniertes CSS für :name", "Config Sms": "SMS konfigurieren", "SMS driver": "SMS-Treiber", "Sms Driver": "SMS-Treiber", "Config Nexmo Driver": "Nexmo-Treiber konfigurieren", "Nexmo Api Key": "Nexmo API-Schlüssel", "Nexmo Api Secret": "Nexmo API-Secret", "From": "<PERSON>", "Config Twilio Driver": "Twilio-Treiber konfigurieren", "Twilio Account Sid": "<PERSON><PERSON><PERSON>-S<PERSON>", "Twilio Account Token": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "SMS Event Booking": "SMS-Event-Buchung", "Phone number must be E.164 format": "Telefonnummer muss im E.164-Format sein", "[+][country code][subscriber number including area code]": "[+][<PERSON><PERSON><PERSON><PERSON>][Teilnehmernummer einschließlich Vorwahl]", "Example": "Beispiel", "Config Phone Administrator": "Administrator-Telefon konfigurier<PERSON>", "Admin Phone": "Admin-Telefon", "Create Booking": "Buchung erstellen", "Administrator": "Administrator", "Customer": "Kunde", "Enable send sms to Administrator when have booking?": "SMS an Administrator senden, wenn Buchung vorhanden?", "Message to Administrator": "<PERSON><PERSON><PERSON><PERSON> an Administrator", "Enable send sms to Vendor when have booking?": "SMS an Anbieter senden, wenn Buchung vorhanden?", "Message to Customer": "Nachricht an Kunde", "Enable send sms to Customer when have booking?": "SMS an Kunde senden, wenn Buchung vorhanden?", "Update booking": "Buchung aktualisieren", "Enable send sms to Administrator when update booking?": "SMS an Administrator senden, wenn Buchung aktualisiert wird?", "Enable send sms to Vendor when update booking?": "SMS an Anbieter senden, wenn Buchung aktualisiert wird?", "Enable send sms to Customer when update booking?": "SMS an Kunde senden, wenn Buchung aktualisiert wird?", "Sms Testing": "SMS-Test", "To (phone number)": "An (Telefonnummer)", "Send Sms Test": "SMS-Test senden", "Main Settings": "Haupteinstellungen", "Modules for Booking Core": "Module für Booking Core", "Manage languages of your website": "Sprachen Ihrer Website verwalten", "Translations": "Übersetzungen", "Translation manager of your website": "Übersetzungsmanager Ihrer Website", "System Log Viewer": "System-Log-Viewer", "Views and manage system log of your website": "System-Logs Ihrer Website anzeigen und verwalten", "Updater": "Updater", "Updater Booking Core": "Booking Core Updater", "Clear Cache": "<PERSON><PERSON> le<PERSON>n", "Clear Cache for Booking Core": "<PERSON><PERSON> für Booking Core leeren", "No tools available": "Keine Werkzeuge verfügbar", "System Updater": "System-Updater", "Update booking core": "Booking Core aktualisieren", "You are using newest version of Booking Core: :version": "Sie verwenden die neueste Version von Booking Core: :version", "Your license key: :key": "<PERSON><PERSON> Lizenzschlüssel: :key", "Last check for update: :date": "Letzte Update-Prüfung: :date", "Last update success: :date": "Letztes erfolgreiches Update: :date", "Check for update": "<PERSON>ch Updates suchen", "Your current version: :version": "Ihre aktuelle Version: :version", "Latest version available: :version": "Neueste verfügbare Version: :version", "I already back up all files and database": "Ich habe bereits alle Dateien und die Datenbank gesichert", "Update now": "Jetzt aktualisieren", "or": "oder", "change license info": "Lizenzinformationen ändern", "License Key Information": "Lizenzschlüssel-Informationen", "Please enter envato username and license key (purchase code) to get autoupdate": "Bitte geben Sie Envato-Benutzername und Lizenzschlüssel (Kaufcode) ein, um automatische Updates zu erhalten", "Envato username": "Envato-Benutzername", "Your license key (Purchase code)": "Ihr Lizenzschlüssel (Kaufcode)", "How can I get my license key?": "Wie kann ich meinen Lizenzschlüssel erhalten?", "Warning": "<PERSON><PERSON><PERSON>", "Please make sure you back up data before updating": "Bitte stellen Si<PERSON> sicher, dass Sie Daten vor dem Update sichern", "Confirmation": "Bestätigung", "Are you want to update now?. Please make sure you backup all your files and database first": "Möchten Si<PERSON> jetzt aktualisieren? Bitte stellen Si<PERSON> sicher, dass Si<PERSON> zu<PERSON>t alle Dateien und die Datenbank sichern", "Notice": "<PERSON><PERSON><PERSON><PERSON>", "Coupon Management": "Gutschein-Verwaltung", "All Coupons": "Alle Gutscheine", "Edit Coupon: :name": "Gutschein bearbeiten: :name", "Create Coupon": "Gutschein erstellen", "Coupon updated": "Gutschein aktualisiert", "Coupon created": "Gutschein erstellt", "Invalid coupon code!": "Ungültiger Gutscheincode!", "Coupon code is applied successfully!": "Gutscheincode wurde erfolgreich angewendet!", "Coupon code is added already!": "Gutscheincode wurde bereits hinzugefügt!", "This coupon code has expired!": "Dieser Gutscheincode ist abgelaufen!", "The order has not reached the minimum value of :amount to apply the coupon code!": "Die Bestellung hat den Mindestwert von :amount nicht erreicht, um den Gutscheincode anzuwenden!", "This order has exceeded the maximum value of :amount to apply coupon code! ": "Diese Bestellung hat den Höchstwert von :amount überschritten, um den Gutscheincode anzuwenden! ", "Coupon code is not applied to this product!": "Gutscheincode gilt nicht für dieses Produkt!", "You need to log in to use the coupon code!": "<PERSON>e müssen sich anmelden, um den Gutscheincode zu verwenden!", "Coupon code is not applied to your account!": "Gutscheincode gilt nicht für Ihr Konto!", "This coupon code has been used up!": "Dieser Gutscheincode wurde aufgebraucht!", "Add new Coupon": "Neuen Gutschein hinzufügen", "General": "Allgemein", "Coupon Code": "Gutscheincode", "Unique Code": "Eindeutiger Code", "Coupon Name": "Gutschein-Name", "Coupon Amount": "Gutschein-Betrag", "Discount Type": "Rabatt-Typ", "Amount": "Betrag", "End Date": "Enddatum", "2021-12-12 00:00:00": "2021-12-12 00:00:00", "Usage Restriction": "Nutzungsbeschränkung", "Minimum Spend": "Mindestausgabe", "No Minimum": "<PERSON><PERSON>", "The Minimum Spend does not include any Booking fee": "Die Mindestausgabe beinhaltet keine Buchungsgebühr", "Maximum Spend": "Höchstausgabe", "No Maximum": "<PERSON><PERSON>", "The Maximum Spend does not include any Booking fee": "Die Höchstausgabe beinhaltet keine Buchungsgebühr", "Only For Services": "Nur für Services", "-- Select Services --": "-- Services auswählen --", "Only For User": "<PERSON><PERSON> <PERSON>utzer", "Usage Limits": "Nutzungslimits", "Usage Limit per Coupon": "Nutzungslimit pro Gutschein", "Unlimited Usage": "Unbegrenzte Nutzung", "Usage Limit Per User": "Nutzungslimit pro Benutzer", "Add new coupon": "Neuen Gutschein hinzufügen", "Code": "Code", "Is Vendor": "Ist An<PERSON>", "No coupon found": "<PERSON><PERSON>in gefunden", "Coupon code": "Gutscheincode", "[Remove]": "[<PERSON><PERSON><PERSON><PERSON>]", "Course": "<PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Category :name": "Kategorie :name", "Category saved": "<PERSON><PERSON><PERSON>", "Courses": "<PERSON><PERSON>", "Course Management": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Course": "<PERSON><PERSON>", "Add new Course": "Neuen Kurs hinzufügen", "Edit Course": "<PERSON><PERSON> bearbeiten", "Course updated": "<PERSON><PERSON> a<PERSON>", "Course created": "<PERSON><PERSON> er<PERSON>", "Lectures Management": "Vorlesungs-Verwaltung", "Course not found": "Kurs nicht gefunden", "Lecture not found": "Vorlesung nicht gefunden", "Lecture updated": "Vorlesung aktualisiert", "Lecture created": "Vorlesung erstellt", "Delete lecture successfully!": "Vorlesung erfolgreich gelöscht!", "Skill Level": "Fertigkeitslevel", "Level": "Level", "Level :name": "Level :name", "Level saved": "Level gespeichert", "Section not found": "Abschnitt nicht gefunden", "Section updated": "Abschnitt aktualisiert", "Section created": "Abschnitt erstellt", "Delete section successfully!": "Abschnitt erfolgreich gelöscht!", "Style 3": "Stil 3", "Filter by Category": "<PERSON><PERSON> Kategorie filtern", "Courses: List Items": "Kurse: Elemente auflisten", "You are not a student of this course": "<PERSON><PERSON> sind kein Student dies<PERSON>", "Manage Courses": "<PERSON><PERSON> verwalten", "Create Courses": "<PERSON><PERSON> er<PERSON>llen", "Course not found!": "Kurs nicht gefunden!", "Edit Courses": "<PERSON><PERSON> bearbeiten", "Manage Course": "<PERSON><PERSON> ver<PERSON><PERSON>", "Booking Report": "Buchungsbericht", "Add video lecture": "Video-Vorlesung hinzufügen", "Add scorm lecture": "SCORM-Vorlesung hinzufügen", "Add presentation lecture": "Präsentations-Vorlesung hinzufügen", "Add iframe lecture": "iFrame-Vorlesung hinzufügen", "Lecture name is required": "Vorlesungsname ist erforderlich", "Section name is required": "Abschnittsname ist erforderlich", "File is required": "Datei ist erforderlich", "Url is required": "URL ist erforderlich", "Duration is required": "<PERSON>uer ist erforderlich", "Search for Courses": "<PERSON><PERSON>", "\":title\" has been added to your cart.": "\":title\" wurde zu Ihr<PERSON> Ware<PERSON> hinzugefügt.", ":duration Hours": ":duration Stunden", ":duration Minutes": ":duration Minuten", "Course Category": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Course Level": "Kurs-Level", "All Courses": "<PERSON><PERSON>", "Add new course": "Neuen Kurs hinzufügen", "Categories": "<PERSON><PERSON><PERSON>", "Levels": "Level", "Course Settings": "Kurs-Einstellungen", "Course Attributes": "Kurs-Attribute", "Courses Availability Calendar": "Kurs-Verfügbarkeitskalender", "Add new category": "Neue Kategorie hinzufügen", "View": "Anzeigen", "Category Content": "Kategorie-Inhalt", "Category name": "Kategorie-Name", "Parent": "Übergeordnet", "Add Category": "<PERSON><PERSON><PERSON>", "Slug": "Slug", "Course Content": "Kurs-Inhalt", "Short Description": "Kurzbeschreibung", "Duration": "<PERSON><PERSON>", "Ex: 100": "Bsp.: 100", "Hours": "Stunden", "If left blank, the total time of the lectures will automatically be calculated": "<PERSON><PERSON> leer gelassen, wird die Gesamtzeit der Vorlesungen automatisch berechnet", "Preview Video Url": "Vorschau-Video-URL", "Video Url": "Video-URL", "Map Engine": "<PERSON>rten-Engine", "Map Lat": "Karten-Breitengrad", "Map Lng": "Karten-Längengrad", "View Course": "<PERSON><PERSON> anzeigen", "Course Options": "Kurs-Optionen", "Course Featured": "<PERSON><PERSON>", "Teacher Setting": "Lehrer-Einstellung", "All Users": "<PERSON><PERSON>", "Add new user": "Neuen Benutzer hinzufügen", "Search User": "<PERSON><PERSON><PERSON> <PERSON>", "Role": "<PERSON><PERSON>", "Change Password": "Passwort ändern", "All Course": "<PERSON><PERSON>", "Teacher": "<PERSON><PERSON><PERSON>", "Students": "<PERSON><PERSON>", "[Teacher Deleted]": "[<PERSON><PERSON><PERSON>]", "No data found": "<PERSON><PERSON> Daten gefunden", "Add Section": "Abschnitt hinzufügen", "Add lecture": "Vorlesung hinzufügen", "Add video": "Video hinzufügen", "Add presentation": "Präsentation hinzufügen", "Add Iframe": "iFrame hinzufügen", "Add SCORM": "SCORM hinzufügen", "Edit section": "<PERSON><PERSON>chnitt bearbeiten", "Remove section": "Abschnitt entfernen", "Edit Lecture": "Vorlesung bearbeiten", "Lecture name": "Vorlesungsname", "File": "<PERSON><PERSON>", "File URL": "Datei-URL", "Duration (minute)": "<PERSON><PERSON> (Minuten)", "in minutes": "in Minuten", "Preview Url": "Vorschau-URL", "Inactive": "Inaktiv", "Display Order": "Anzeigereihenfolge", "Edit Section": "<PERSON><PERSON>chnitt bearbeiten", "Section name": "Abschnittsname", "Add new level": "Neues Level hinzufügen", "Level Content": "Level-Inhalt", "Level name": "Level-Name", "Add level": "Level hinzufügen", "Sub Title Page": "Untertitel-Seite", "Config review for course": "Bewertung für Kurs konfigurieren", "Enable review system for Course?": "Bewertungssystem für Kurs aktivieren?", "Turn on the mode for reviewing course": "Modus für Kurs-Bewertung aktivieren", "Customer must book a course before writing a review?": "Kunde muss <PERSON> buchen, bevor er eine Bewertung schreibt?", "Config buyer fees for course": "Käufergebühren für Kurs konfigurieren", "Teacher Options": "Lehrer-Optionen", "Teacher config for course": "Lehrer-Konfiguration für Kurs", "Job created by vendor must be approved by admin": "Von <PERSON>ter erstellter Job muss vom Admin genehmigt werden", "Disable course module?": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "Disable course module": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Course name": "<PERSON>rs-Name", "Ratings": "Bewertungen", "Instructors": "Dozenten", "Class icon featured": "Klassen-Icon <PERSON>", "Welcome :name!": "Willkommen :name!", "Earning statistics": "Verdienststatistiken", "Recent Bookings": "Aktuelle Buchungen", "More": "<PERSON><PERSON>", "Item": "Element", "Created At": "Erstellt am", "[Deleted]": "[<PERSON><PERSON><PERSON><PERSON><PERSON>]", "Timeline": "Zeitlinie", "Currency: :currency_main": "Währung: :currency_main", "Yesterday": "Gestern", "Last 7 Days": "Letzte 7 Tage", "Last 30 Days": "Letzte 30 Tage", "This Month": "<PERSON><PERSON>", "Last Month": "Letzter Monat", "This Year": "<PERSON><PERSON>", "This Week": "<PERSON><PERSON>", "DEMO MODE: Disable update": "DEMO-MODUS: Update deaktiviert", "Email Settings": "E-Mail-Einstellungen", "Config Email": "E-Mail konfigurieren", "Change your config email site": "Ihre E-Mail-Website-Konfiguration ändern", "Email Driver": "E-Mail-Treiber", "Email Host": "E-Mail-Host", "Email Port": "E-Mail-Port", "Email Encryption": "E-Mail-Verschlüsselung", "Email Username": "E-Mail-Benutzername", "Email Password": "E-Mail-Passwort", "Mailgun Domain": "Mailgun-Domain", "Mailgun Secret": "Mailgun-Secret", "Mailgun Endpoint": "Mailgun-Endpunkt", "Postmark Token": "Postmark-Token", "Ses Key": "SES-Schlüssel", "Ses Secret": "SES-Secret", "Ses Region": "SES-Region", "Sparkpost Secret": "Sparkpost-Secret", "Email From Config": "E-Mail-Von-Konfiguration", "How your customer can contact to you": "Wie Ihre Kunden Sie kontaktieren können", "Admin Email": "Admin-E-Mail", "You will get all notifications from this email": "Sie erhalten alle Benachrichtigungen von dieser E-Mail", "Email Form Name": "E-Mail-Formular-Name", "Email Form Address": "E-Mail-Formular-<PERSON><PERSON><PERSON>", "Email Testing": "E-Mail-Test", "Send Email Test": "E-Mail-Test senden", "Email Header & Footer": "E-Mail-Kopf- und Fußzeile", "Change booking email header and footer": "Buchungs-E-Mail-Kopf- und Fußzeile ändern", "Header": "Kopfzeile", "Event": "Event", "Events": "Events", "Event Management": "Event-<PERSON>erwaltung", "Recovery Event Management": "Event-Verwaltung wiederherstellen", "Add Event": "Event hinzufügen", "Add new Event": "Neues Event hinzufügen", "Edit Event": "Event bearbeiten", "Event updated": "Event aktualisiert", "Event created": "Event erstellt", "Service Event": "Service Event", "Event: Form Search": "Event: Suchformular", "Event: List Items": "Event: Elemente auflisten", "Events Availability": "Event-Verfügbarkeit", "Event not found": "Event nicht gefunden", ":count events found": ":count Events gefunden", ":count event found": ":count Event gefunden", "Showing :from - :to of :total Events": "Zeige :from - :to von :total Events", "Manage Events": "<PERSON> verwalten", "Recovery Events": "Events wiederherstellen", "Restore event success!": "Event erfolgreich wiederhergestellt!", "Create Events": "Events erstellen", "Event not found!": "Event nicht gefunden!", "Edit Events": "Events bearbeiten", "Delete event success!": "Event erfolgreich gelöscht!", "Search for Events": "Nach Events suchen", "Start date is not a valid date": "Startdatum ist kein gültiges Datum", "Please select ticket!": "Bitte Ticket auswählen!", "There are :numberTicket :titleTicket available for your selected date": "Es sind :numberTicket :titleTicket für Ihr ausgewähltes Datum verfügbar", "Please select start time!": "Bitte Startzeit auswählen!", ":slot not available for your selected ": ":slot nicht verfügbar für Ihre Auswahl ", "ticket": "Ticket", ":number Events": ":number Events", ":number Event": ":number Event", "All Events": "Alle Events", "Manage Event": "Event verwalten", "Event Settings": "Event-Einstellungen", "Event Attributes": "Event-Attribute", "Events Availability Calendar": "Event-Verfügbarkeitskalender", "Showing :from - :to of :total events": "Zeige :from - :to von :total Events", "No events found": "Keine Events gefunden", "Add new event": "Neues Event hinzufügen", "View Event": "Event anzeigen", "Event Featured": "Event hervorgehoben", "Event Content": "Event-Inhalt", "Start Time": "Startzeit", "Ex: 15:00": "Bsp.: 15:00", "Input time format, ex: 15:00": "Zeitformat eingeben, Bsp.: 15:00", "End Time": "Endzeit", "Ex: 21:00": "Bsp.: 21:00", "Input time format, ex: 21:00": "Zeitformat eingeben, Bsp.: 21:00", "Duration (hour)": "<PERSON>uer (Stunden)", "Duration Unit": "Dauer-Einheit", "Hour": "Stunde", "Minute": "Minute", "Event Price": "Event-Preis", "Event Sale Price": "Event-Verkaufspreis", "Tickets": "Tickets", "Price - Number": "Preis <PERSON> <PERSON><PERSON><PERSON>", "ticket_vip_1": "ticket_vip_1", "Price Ticket": "Ticket-Preis", "Number Ticket": "Ticket-<PERSON><PERSON><PERSON>", "Per hour": "Pro Stunde", "Price per ticket": "Preis pro Ticket", "No event found": "Kein Event gefunden", "Config review for event": "Bewertung für Event konfigurieren", "Enable review system for Event?": "Bewertungssystem für Event aktivieren?", "Turn on the mode for reviewing event": "Modus für Event-Bewertung aktivieren", "Customer must book a event before writing a review?": "Kunde muss <PERSON> buchen, bevor er eine Bewertung schreibt?", "Booking Options": "Buchungsoptionen", "Config Booking for event": "Buchung für Event konfigurieren", "Booking Type": "Buchungstyp", "Ticket": "Ticket", "Time slot": "Zeitfenster", "Booking Buyer Fees": "Buchungs-Käufergebühren", "Vendor config for event": "Anbieter-Konfiguration für Event", "Event created by vendor must be approved by admin": "<PERSON> erstelltes Event muss vom Admin genehmigt werden", "Disable event module?": "Event-<PERSON><PERSON><PERSON>?", "Disable event module": "Event-<PERSON><PERSON><PERSON>", "Event information": "Event-<PERSON>en", "Event name": "Event-Name", "Duration:": "Dauer:", ":count hour": ":count <PERSON><PERSON><PERSON>", ":count hours": ":count <PERSON><PERSON><PERSON>", "Start Time:": "Startzeit:", "Airline": "Fluggesellschaft", "Airline Management": "Fluggesellschafts-Verwaltung", "Edit airline": "Fluggesellschaft bearbeiten", "Airline saved": "Fluggesellschaft gespeichert", "Airport": "Flughafen", "Airport Management": "Flughafen-Verwaltung", "Edit airport": "Flughafen bearbeiten", "Airport saved": "Flughafen gespeichert", "Import Queued": "Import in Warteschlange", "Flight": "Flug", "Flights": "Flüge", "Flight Management": "Flug-Verwaltung", "Recovery Flight Management": "Flug-Verwaltung wiederherstellen", "Add Flight": "Flug hinzufügen", "Add new Flight": "Neuen Flug hinzufügen", "Edit Flight": "Flug bearbeiten", "Edit: #:name": "Bearbeiten: #:name", "Flight updated": "Flug aktualisiert", "Flight created": "Flug erstellt", "Flight: :name :code #:id": "Flug: :name :code #:id", "Flight seat": "Flugsitz", "Flight seat Management": "Flugsitz-Verwaltung", "Edit flight_seat": "Flugsitz bearbeiten", "Flight seat saved": "Flugsitz gespeichert", "Seat Type": "Sitztyp", "Seat Type Management": "Sitztyp-Verwaltung", "Seat type": "Sitztyp", "Edit seat type": "Sitztyp bearbeiten", "Seat type saved": "Sitztyp gespeichert", "Flight: Form Search": "Flug: Such<PERSON>ular", "Flight Blocks": "Flug-B<PERSON><PERSON>cke", ":count flights found": ":count <PERSON><PERSON><PERSON><PERSON> gefunden", ":count flight found": ":count Flug gefunden", "Showing :from - :to of :total Flights": "Zeige :from - :to von :total Flügen", "Manage Flights": "Flüge verwalten", "Recovery Flights": "Flüge wiederherstellen", "Restore flight success!": "Flug erfolgreich wiederhergestellt!", "Create Flights": "Flüge erstellen", "Flight not found!": "Flug nicht gefunden!", "Edit Flights": "Flüge bearbeiten", "Delete flight success!": "Flug erfolgreich gelöscht!", "Flight clone was successful": "Flug-Klonen war erfolgreich", "Flight: :name": "Flug: :name", "All Flight seats": "Alle Flugsitze", "Create Flight seat": "Flugsitz erstellen", "Edit  :name": "Bearbeiten :name", "Flight seat updated": "Flugsitz aktualisiert", "Flight seat created": "Flugsitz erstellt", "Delete room success!": "Raum erfolgreich gelöscht!", "Search for Flights": "<PERSON><PERSON> Flügen suchen", ":number Flights": ":number Flüge", ":number Flight": ":number Flug", "All Flights": "Alle Flüge", "Manage Flight": "Flug verwalten", "Add Flights": "Flüge hinzufügen", "Flight Settings": "Flug-Einstellungen", "Airline Content": "Fluggesellschafts-Inhalt", "Airline: :name": "Fluggesellschaft: :name", "Add Airline": "Fluggesellschaft hinzufügen", "Airport Content": "Flughafen-Inhalt", "IATA Code": "IATA-Code", "Airport: :name": "Flughafen: :name", "Import from IATA": "Von IATA importieren", "Add Airport": "Flughafen hinzufügen", " Mark as Publish ": " Als veröffentlicht markieren ", " Mark as Draft ": " Als Entwurf markieren ", "Found :count airport(s)": ":count Flughafen/Flughäfen gefunden", "Flight Attributes": "Flug-Attribute", "Add new flight": "Neuen Flug hinzufügen", " Flight Ticket type": " Flugticket-Typ", "Add new seat type": "Neuen Sitztyp hinzufügen", "Flight Content": "Flug-Inhalt", "-- Select Airport from --": "-- Abflughafen auswählen --", "To": "Nach", "-- Select Airport to --": "-- Zielflughafen auswählen --", "Airline and time": "Fluggesellschaft und Zeit", "-- Select Airline --": "-- Fluggesellschaft auswählen --", "Departure time": "Abflugzeit", "Arrival time": "Ankunftszeit", "hours": "Stunden", "Search by code": "Nach Code suchen", "All Flight": "Alle Flüge", "-- Select seat type --": "-- Sitztyp auswählen --", "Max passengers": "<PERSON><PERSON>", "Person type": "Personentyp", "Adult": "Erwachsener", "Child": "Kind", "Baggage Check in": "Aufgabegepäck", "Baggage Cabin": "Handgepäck", "Add Flight Seat": "Flugsitz hinzufügen", "All Flight seat": "Alle Flugsitze", "Airport From": "Abflughafen", "Airport To": "Zielflughafen", "Flight ticket": "Flugticket", "No flight found": "<PERSON><PERSON>lug gefunden", "Seat type Content": "Sitztyp-Inhalt", "Seat Type: :name": "Sitztyp: :name", "Add Seat Type": "Sitztyp hinzufügen", "From where": "<PERSON> wo", "To where": "W<PERSON>in", "Config review for flight": "Bewertung für Flug konfigurieren", "Enable review system for Flight?": "Bewertungssystem für Flug aktivieren?", "Turn on the mode for reviewing flight": "Modus für Flug-Bewertung aktivieren", "Customer must book a flight before writing a review?": "Kunde muss Flug buchen, bevor er eine Bewertung schreibt?", "Config Booking for flight": "Buchung für Flug konfigurieren", "Flight by day": "Flug am Tag", "Flight by night": "Flug in der Nacht", "Vendor config for flight": "Anbieter-Konfiguration für Flug", "Flight created by vendor must be approved by admin": "<PERSON> erstellter Flug muss vom Admin genehmigt werden", "Disable flight module?": "Flug-<PERSON><PERSON><PERSON>?", "Disable flight module": "Flug-<PERSON><PERSON><PERSON>", "Flight information": "Flug-Informationen", "Flight name": "Flug-Name", "Nights:": "Nächte:", "Rental price": "Mietpreis", "Hotel": "Hotel", "Hotels": "Hotels", "Hotel Management": "Hotel-Verwaltung", "Add Hotel": "Hotel hinzufügen", "Add new Hotel": "Neues Hotel hinzufügen", "Recovery Hotel Management": "Hotel-Verwaltung wiederherstellen", "Edit Hotel": "Hotel bearbeiten", "Hotel updated": "Hotel aktualisiert", "Hotel created": "Hotel erstellt", "Room Attributes": "Zimmer-Attribute", "Hotel: :name": "Hotel: :name", "Room Management": "Zimmer-Verwaltung", "All Rooms": "<PERSON><PERSON>", "Edit room: :name": "Zimmer bearbeiten: :name", "Room updated": "<PERSON><PERSON> aktualisiert", "Room created": "<PERSON><PERSON> er<PERSON>", "Hotel: Form Search": "Hotel: Suchformular", "Service Hotel": "Service Hotel", "Hotel: List Items": "Hotel: Elemente auflisten", "Room Availability": "Zimmer-Verfügbarkeit", "Hotel not found": "Hotel nicht gefunden", "room not found": "<PERSON>immer nicht gefunden", "Room not found": "<PERSON>immer nicht gefunden", ":count hotels found": ":count Hotels gefunden", ":count hotel found": ":count Hotel gefunden", "Showing :from - :to of :total Hotels": "Zeige :from - :to von :total Hotels", "Dates are not valid": "<PERSON>rmine sind nicht gültig", "Maximum day for booking is 30": "Maximale Buchungsdauer beträgt 30 Tage", "Manage Hotels": "Hotels verwalten", "Recovery Hotels": "Hotels wiederherstellen", "Create Hotels": "Hotels erstellen", "Space not found!": "Raum nicht gefunden!", "Edit Hotels": "Hotels bearbeiten", "Delete hotel success!": "Hotel erfolgreich gelöscht!", "Restore hotel success!": "Hotel erfolgreich wiederhergestellt!", "Create Room": "<PERSON><PERSON>", "Search for Spaces": "<PERSON><PERSON> R<PERSON> suchen", "Please select at lease one room": "Bitte wählen Sie mindestens ein Zimmer", "There is no room available at your selected dates": "Es ist kein Z<PERSON> an Ihren ausgewählten Terminen verfügbar", "Your selected room is not available. Please search again": "Ihr ausgewähltes Zimmer ist nicht verfügbar. Bitte suchen Si<PERSON> erneut", "The :name need to select at least :number days": "Das :name muss mindestens :number Tage auswählen", "Sorry, the current rooms are not enough for adults": "Entschuldigung, die aktuellen Zimmer reichen nicht für Erwachsene", "Sorry, the current rooms are not enough for children": "Entschuldigung, die aktuellen Zimmer reichen nicht für Kinder", "Please select check-in and check-out date": "Bitte Check-in- und Check-out-<PERSON><PERSON> auswählen", "rooms": "<PERSON><PERSON>", "room": "<PERSON><PERSON>", ":number Hotels": ":number Hotels", ":number Hotel": ":number Hotel", ":count nights": ":count <PERSON><PERSON><PERSON><PERSON>", ":count night": ":count <PERSON><PERSON>", "Hotel Star": "Hotel-Sterne", "Rooms": "<PERSON><PERSON>", "Hotel Room": "Hotelzimmer", "All Hotels": "Alle Hotels", "Manage Hotel": "Hotel verwalten", "Hotel Settings": "Hotel-Einstellungen", "Hotel Attributes": "Hotel-Attribute", "Add new hotel": "Neues Hotel hinzufügen", "Manage Rooms": "<PERSON><PERSON> ver<PERSON><PERSON>", "View Hotel": "Hotel anzeigen", "Hotel Featured": "Hotel hervorgehoben", "Hotel Content": "Hotel-Inhalt", "Name of the hotel": "Name des Hotels", "Hotel Policy": "Hotel-Richtlinie", "Hotel rating standard": "Hotel-Bewertungsstandard", "Eg: 5": "Z.B.: 5", "Policy": "Rich<PERSON><PERSON><PERSON>", "Eg: What kind of foowear is most suitable ?": "Z.B.: Welche Art von Schuhwerk ist am besten geeignet?", "Check in/out time": "Check-in/Check-out-<PERSON><PERSON>", "Allowed full day booking": "Ganztägige Buchung erlaubt", "Enable full day booking": "Ganztägige Buchung aktivieren", "You can book room with full day": "<PERSON>e können Z<PERSON> ganztägig buchen", "Eg: booking from 22-23, then all days 22 and 23 are full, other people cannot book": "Z.B.: <PERSON>uchung vom 22.-23., dann sind alle Tage 22 und 23 belegt, andere können nicht buchen", "Time for check in": "Zeit für Check-in", "Eg: 12:00AM": "Z.B.: 12:00", "Time for check out": "Zeit für Check-out", "Eg: 11:00AM": "Z.B.: 11:00", "Hotel Price": "Hotel-Preis", "Hotel Sale Price": "Hotel-Verkaufspreis", "Surroundings": "Umgebung", "Distance": "Entfernung", "Sunny Beach": "Sonnenstrand", "m": "m", "km": "km", "Edit hotel": "Hotel bearbeiten", "Manage Rooms Availability": "Zimmer-Verfügbarkeit verwalten", "No hotel found": "Kein Hotel gefunden", "Room Availability Calendar": "Zimmer-Verfügbarkeitskalender", "Showing :from - :to of :total rooms": "Zeige :from - :to von :total Zimmern", "No rooms found": "<PERSON><PERSON> gefunden", "Number of room": "<PERSON><PERSON><PERSON>", "Add new Hotel Room": "Neues Hotelzimmer hinzufügen", "Room information": "Zimmer-Informationen", "Room Name": "<PERSON><PERSON>-Name", "Room name": "<PERSON><PERSON>-Name", "Room Description": "Zimmer-Beschreibung", "Number of beds": "<PERSON><PERSON><PERSON>", "Room Size": "Zimmergröße", "Room size": "Zimmergröße", "Max Adults": "<PERSON><PERSON>", "Max Children": "<PERSON><PERSON>", "Back to hotel": "Zurück zum Hotel", "Add Room": "<PERSON><PERSON>", "No room found": "<PERSON><PERSON> gefunden", "Guests": "<PERSON><PERSON><PERSON>", "Layout Item Hotel In Page Search": "Layout-Element Hotel in Seitensuche", "List Item": "Listenelement", "Grid Item": "Rasterelement", "Which attribute show in listing page?": "Welches Attribut in der Auflistungsseite anzeigen?", "Config review for hotel": "Bewertung für Hotel konfigurieren", "Enable review system for Hotel?": "Bewertungssystem für Hotel aktivieren?", "Turn on the mode for reviewing hotel": "Modus für Hotel-Bewertung aktivieren", "Customer must book a hotel before writing a review?": "Kunde muss Hotel buchen, bevor er eine Bewertung schreibt?", "Config buyer fees for hotel": "Käufergebühren für Hotel konfigurieren", "Vendor config for hotel": "Anbieter-Konfiguration für Hotel", "Hotel created by vendor must be approved by admin": "Von Anbieter erstelltes Hotel muss vom Admin genehmigt werden", "Disable hotel module?": "Hotel-<PERSON><PERSON><PERSON>?", "Disable hotel module": "Hotel-<PERSON><PERSON><PERSON>", "Hotel information": "Hotel-Informationen", "Hotel name": "Hotel-Name", "Check in": "Check-in", "Check out:": "Check-out:", "Language created": "Sprache erstellt", "Language Management": "Sprachen-Verwaltung", "Language updated": "Sprache aktualisiert", "Translate for: :name": "Übersetzen für: :name", "Translation saved": "Übersetzung gespeichert", "Folder: resources/lang is not write-able. Please contact your hosting provider": "Ordner: resources/lang ist nicht beschreibbar. Bitte kontaktieren Sie Ihren Hosting-Anbieter", "File: :file_name is not write-able. Please contact your hosting provider": "Datei: :file_name ist nicht beschreibbar. Bitte kontaktieren Sie Ihren Hosting-Anbieter", "Re-build language file for: :name success": "Sprachdatei für :name erfolgreich neu erstellt", "Default language source does not exists": "Standard-<PERSON><PERSON><PERSON><PERSON><PERSON> existiert nicht", "Default language source empty": "Standard-<PERSON><PERSON><PERSON><PERSON><PERSON> ist leer", "Default language source do not have any strings": "Standard-S<PERSON>ch<PERSON>le hat keine Zeichenketten", "Loaded :count strings": ":count <PERSON><PERSON><PERSON><PERSON><PERSON>", "Generate Default JSON Language": "Standard-JSON-Sprache generieren", "File language source does not exists": "Datei-<PERSON><PERSON><PERSON><PERSON><PERSON> exist<PERSON>t nicht", "File language source empty": "Date<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> ist leer", "File language source do not have any strings": "Datei-<PERSON><PERSON><PERSON><PERSON><PERSON> hat keine Zeichenketten", "Load language from json success": "Sprache aus JSON erfolgreich geladen", "Add new location": "Neuen Standort hinzufügen", "Language Content": "Sprach-Inhalt", "Locale": "Gebietsschema", "Flag Icon": "Flaggen-Icon", "Eg: gb": "Z.B.: de", "Please input flag code": "Bitte Flaggen-Code eingeben", "Display Name": "Anzeigename", "Please input language name": "Bitte Sprachname eingeben", "Add Language": "Sprache hinzufügen", "All Languages": "Alle Sprachen", "Translate Manager for: :name": "Übersetzungsmanager für: :name", "All text": "Alle Texte", "Not translated": "Nicht übersetzt", "Translated": "Übersetzt", "Search By": "<PERSON><PERSON> nach", "Original Text": "Originaltext", "Translated Text": "Übersetzter Text", "Search by key ...": "<PERSON><PERSON> suchen ...", "Filter": "Filter", "Found :total texts": ":total Texte gefunden", "Translate": "Übersetzen", "Origin": "Ursprung", "Find Translations": "Übersetzungen finden", "After translation. You must re-build language file to apply the change": "Nach der Übersetzung müssen Sie die Sprachdatei neu erstellen, um die Änderung anzuwenden", "Last build at": "Zuletzt erstellt am", "Build": "<PERSON><PERSON><PERSON><PERSON>", "Booking Core by": "Booking Core von", "https://www.bookingcore.co": "https://www.bookingcore.co", "BookingCore Team": "BookingCore Team", "About Us": "Über uns", "https://m.me/bookingcore": "https://m.me/bookingcore", "Contact Us": "Kontaktieren Sie uns", "Please check the form below for errors": "Bitte überprüfen Sie das untenstehende Formular auf Fehler", "Do you want to restore?": "Möchten Sie wiederherstellen?", "Confirm": "Bestätigen", "Custom Range": "Benutzerdefinierter Bereich", "W": "W", "Su": "So", "Mo": "Mo", "Tu": "Di", "We": "<PERSON>", "Th": "Do", "Fr": "Fr", "Sa": "Sa", "January": "<PERSON><PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON>", "March": "<PERSON><PERSON><PERSON>", "April": "April", "May": "<PERSON>", "June": "<PERSON><PERSON>", "July": "<PERSON><PERSON>", "August": "August", "September": "September", "October": "Oktober", "November": "November", "December": "Dezember", "Image Editor": "Bildbearbeitung", "Toggle fullscreen": "Vollbild umschalten", "Close window": "Fenster schließen", "Save": "Speichern", "Save As New Image": "Als neues Bild speichern", "Go Back": "Zurück gehen", "Adjust": "<PERSON><PERSON><PERSON>", "Effects": "Effekte", "Filters": "Filter", "Orientation": "Ausrichtung", "Crop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Resize": "Größe ändern", "Watermark": "Wasserzeichen", "Focus point": "Fokuspunkt", "Shapes": "Formen", "Brightness": "Helligkeit", "Contrast": "<PERSON><PERSON><PERSON><PERSON>", "Exposure": "Belichtung", "Saturation": "Sättigung", "Rotate Left": "Nach links drehen", "Rotate Right": "Nach rechts drehen", "Flip Horizontally": "Horizontal spiegeln", "Flip Vertically": "Vertikal spiegeln", "Would you like to reduce resolution before editing the image?": "Möchten Sie die Auflösung vor der Bildbearbeitung reduzieren?", "Keep original resolution": "Ursprüngliche Auflösung beibehalten", "Resize & Continue": "Größe ändern & Fortfahren", "Reset": "Z<PERSON>ücksetzen", "Undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Processing...": "Verarbeitung...", "The resolution of the image is too big for the web. It can cause problems with Image Editor performance.": "Die Auflösung des Bildes ist zu groß für das Web. Es kann Probleme mit der Bildbearbeitungsleistung verursachen.", "x": "x", "y": "y", "width": "Breite", "height": "<PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "original": "original", "square": "quadratisch", "Opacity": "Deckkraft", "Apply watermark": "Wasserzeichen anwenden", "Upload": "Hochladen", "Home": "Startseite", "Upgrade": "Upgrade", "Mark all as read": "Alle als gelesen markieren", "Notifications": "Benachrichtigungen", "View More": "<PERSON><PERSON> anzeigen", "Edit Profile": "<PERSON><PERSON>", "Vendor Dashboard": "Anbieter-Dashboard", "Logout": "Abmelden", "Email address": "E-Mail-Adresse", "Remember me": "Ang<PERSON><PERSON><PERSON> bleiben", "Forgot Password?": "Passwort vergessen?", "or continue with": "oder fortfahren mit", "Do not have an account?": "<PERSON>ben <PERSON> kein Konto?", "Reset Password": "Passwort zurücksetzen", "E-Mail Address": "E-Mail-Adresse", "Send Password Reset Link": "Passwort-Reset-Link senden", "Confirm Password": "Passwort bestätigen", "I have read and accept the <a href=':link' target='_blank'>Terms and Privacy Policy</a>": "Ich habe die <a href=':link' target='_blank'>Geschäftsbedingungen und Datenschutzrichtlinie</a> gelesen und akzeptiert", " Already have an account?": " Haben <PERSON> bereits ein Konto?", "Log In": "Anmelden", "Register": "Registrieren", "Verify Your Email Address": "Verifizieren Sie Ihre E-Mail-Adresse", "A fresh verification link has been sent to your email address.": "Ein neuer Verifizierungslink wurde an Ihre E-Mail-Adresse gesendet.", "Before proceeding, please check your email for a verification link.": "<PERSON><PERSON> fort<PERSON>hren, überprüfen Sie bitte Ihre E-Mail auf einen Verifizierungslink.", "If you did not receive the email": "Wenn Sie die E-Mail nicht erhalten haben", "click here to request another": "klicken <PERSON> hier, um eine weitere anzu<PERSON>ern", "We use cookies!": "Wir verwenden Cookies!", "Reject all": "<PERSON>e <PERSON>en", "Get Updates & More": "Updates & Mehr erhalten", "Thoughtful thoughts to your inbox": "Durchdachte Gedanken in Ihren Posteingang", "Your Email": "Ihre E-Mail", "Subscribe": "Abonnieren", "Hi, :Name": "Hallo, :Name", "My profile": "<PERSON><PERSON>", "Messages": "Nachrichten", "Change password": "Passwort ändern", "Admin Dashboard": "Admin-Dashboard", "Hi, :name": "Hallo, :name", "Credit: :amount": "Guthaben: :amount", "My plan": "Mein <PERSON>", "Support Center": "Support-Center", "Location updated": "Standort aktualisiert", "Location created": "Standort erstellt", "Service Type": "Service-Typ", "Style 4": "Stil 4", "List Location by IDs": "Standorte nach IDs auflisten", "Link to location detail page?": "Link zur Standort-Detailseite?", "List Locations": "<PERSON><PERSON><PERSON> auflisten", "Location Category": "Standort-<PERSON><PERSON><PERSON>", "All Location": "<PERSON><PERSON> Standorte", "All Category": "Alle Kategorien", "Icon class": "Icon-Klasse", "Location Categories": "Standort-<PERSON><PERSON><PERSON>", "Location Content": "Standort-Inhalt", "Trip Ideas": "Reiseideen", "Title/Link": "Titel/Link", "Title:": "Titel:", "Link:": "Link:", "Location name": "Standort-Name", "Add Location": "Standort hinzufügen", "Location Map": "Standort-Karte", "Click onto map to place Location address": "<PERSON><PERSON> klicken, um Standort-Adresse zu platzieren", "Media Management": "", "Please log in": "", "Can not remove!": "", "Please select file": "", "You don't have permission delete the file!": "", "Delete the file success!": "", "File not found!": "", "403": "", "You are not allowed to edit this folder": "", "Folder name exists, please select new one": "", "You are not allowed to delete this folder": "", "Folder deleted": "", "Can not upload the file": "", "File type are not allowed": "", "Maximum upload file size is :max_size B": "", "Can not get image dimensions": "", "Maximum width allowed is: :number": "", "Maximum height allowed is: :number": "", "Upload image": "", "Select images": "", "Can not edit non-local images": "", "Update Successful": "", "Media": "", "File type invalid": "", "Can not get image size": "", "Media Settings": "", "Folder not found. Please try again": "", "Can not upload file": "", "Search file name....": "", "files": "", "Add Folder": "", "No file found": "", "Previous": "", "Next": "", "Delete file": "", "file selected": "", "unselect": "", "Use file": "", "Cloud Storage Configs": "", "Select Cloud Driver": "", "-- Local Storage --": "", "AWS S3": "", "Google Cloud Storage": "", "Amazon S3": "", "Key": "", "Secret access key": "", "Default region": "", "Bucket": "", "Project ID": "", "Service Account Key File Name": "", "View file": "", "Folder": "", "Delete this folder": "", "Category updated": "", "Category created": "", "Please select an Action!": "", "News Management": "", "Add News": "", "News updated": "", "News created": "", "News does not exists": "", "Language does not exists": "", "Tag": "", "Tag updated": "", "Tag Created": "", "News: List Items": "", "Search results : \":s\"": "", "News Tag": "", "New Tag": "", "All News": "", "Tags": "", "Manage News": "", "News Settings": "", "Permalink:": "", "News Categories": "", "Search Category": "", "Edit post: ": "", "Add new Post": "", "View Post": "", "News content": "", "Enter tag": "", "All news": "", " Move to Pending ": "", "--All Category --": "", "Search News": "", "Page List": "", "Config page list news of your website": "", "Posts Per Page": "", "Sidebar Options": "", "Config sidebar for news": "", "Title: About Us": "", "Search Form": "", "Recent News": "", "Featured Listings": "", "Content Text": "", "Vendor News": "", "Config for vendor": "", "Admin need approve news to be publish": "", "Tag Content": "", "Tag name": "", "Tag Slug": "", "News Tags": "", "Add Tag": "", "Search keyword ...": "", "Search Tag": "", "Page Management": "", "Pages": "", "Add Page": "", "Edit Page": "", "Page updated": "", "Page created": "", "Header Style": "", "Transparent": "", "Add new page": "", "Permalink: ": "", "Template Builder": "", "View page": "", "Page Content": "", "All Page": "", "Search Page": "", "Popups": "", "Popup Management": "", "Recovery Popup Management": "", "Add Popup": "", "Add new Popup": "", "Edit Popup": "", "Popup updated": "", "Popup created": "", "Popup": "", "Add new popup": "", "Preview": "", "All Popups": "", "No popup found": "", "Show on": "", "Include URLs": "", "Wildcard allowed. Eg: */checkout/* ": "", "Exclude URLs": "", "Popup name": "", "Schedule": "", "Show every": "", "Day": "", "Month": "", "Year": "", "Property": "", "Properties": "", "Property Management": "", "Add Property": "", "Add new Property": "", "Edit Property": "", "Property updated": "", "Property created": "", "Contact property": "", "Title Link More": "", "Link More": "", "Background Color - get code in <a href=\"https://html-color-codes.info\" target=\"_blank\">https://html-color-codes.info</a>": "", "Style 1 : Background Color Only": "", "Style 2 : Background Image": "", "Style 3 : Background Image + Color": "", "Call To Action": "", "Property: Form Search": "", "Layout": "", "Carousel Layout": "", "View All Option": "", "View All With 3 Items Grid": "", "Hide button scroll down?": "", "Property: List Items": "", "Filter by Role": "", "User: List Users": "Benutzer: <PERSON><PERSON><PERSON> auflisten", "Select term property": "Immobilien-Begriff auswählen", "Property: Term Featured Box": "Immobilie: Begriff-Hervorhebungsbox", "Sub title": "Untertitel", "Number star": "<PERSON><PERSON><PERSON>", "Position": "Position", "List Testimonial": "Testimonials auflisten", "Properties Availability": "Immobilien-Verfügbarkeit", "Property not found": "Immobilie nicht gefunden", "Manage Properties": "<PERSON>mmob<PERSON><PERSON> verwalten", "Create Properties": "Immob<PERSON><PERSON> er<PERSON>llen", "Property not found!": "Immobilie nicht gefunden!", "Edit Properties": "Immob<PERSON>en bearbeiten", "Delete property success!": "Immobilie erfolgreich gelöscht!", "Manage Property": "<PERSON><PERSON><PERSON><PERSON><PERSON> ver<PERSON><PERSON>", "Property clone was successful": "Immobilien-Klonen war erfolgreich", "Showing :from - :to of :total properties": "Zeige :from - :to von :total Immobilien", "Search for Properties": "Nach Immobilien suchen", "Maximum guests is :count": "Maximale Gästeanzahl ist :count", "This property is not available at selected dates": "Diese Immobilie ist an den ausgewählten Terminen nicht verfügbar", ":number Properties": ":number Immobilien", ":number Property": ":number <PERSON><PERSON><PERSON><PERSON><PERSON>", "From ": "<PERSON> ", "For Buy": "<PERSON><PERSON>", "For Rent": "<PERSON><PERSON>", "Property Category": "Immobilien-<PERSON><PERSON><PERSON>", "All Properties": "Alle Immobilien", "Property Settings": "Immobilien-Einstellungen", "Property Attributes": "Immobilien-Attribute", "Properties Availability Calendar": "Immobilien-Verfügbarkeitskalender", "No properties found": "<PERSON>ine Immobilien gefunden", "Property Categories": "Immobilien-Kate<PERSON>ien", "Name of property": "Name der Imm<PERSON><PERSON>e", "Name of vendor": "Name des Anbieters", "Add new property": "Neue Immobilie hinzufügen", "View Property": "<PERSON><PERSON><PERSON><PERSON><PERSON> anzeigen", "Property type": "Immobilientyp", "For buy": "<PERSON><PERSON>", "For rent": "<PERSON><PERSON>", "Property Featured": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sold": "Verkauft", "Sold Out": "Ausverkauft", "No property found": "<PERSON><PERSON> Immobilie gefunden", "Property Content": "Immobilien-Inhalt", "Video Background": "Video-Hi<PERSON>grund", "No. Bed": "<PERSON><PERSON><PERSON>", "No. Bathroom": "<PERSON><PERSON><PERSON>", "Square": "Quadrat", "Example: 100": "Beispiel: 100", "Garages": "Garagen", "Year built": "<PERSON><PERSON><PERSON><PERSON>", "Example: 2020": "Beispiel: 2020", "Area": "Fläche", "Additional details": "Zusätzliche Details", "Deposit": "<PERSON><PERSON>", "Pool size": "Pool-Größe", "Additional zoom": "Zusätzlicher Zoom", "Remodal year": "Renovierungsjahr", "Amenities": "Ausstattung", "Equipment": "Ausrüstung", "Property Price": "Immobilien-Preis", "Property Sale Price": "Immobilien-Verkaufspreis", "Max Guests": "<PERSON><PERSON>", "Discount by number of people": "<PERSON><PERSON><PERSON> nach Personenanzahl", "No of people": "<PERSON><PERSON><PERSON>", "Discount": "<PERSON><PERSON><PERSON>", "Percent (%)": "Prozent (%)", "Property Type": "Immobilientyp", "Bathrooms": "<PERSON><PERSON><PERSON>", "Bedrooms": "Schlafzimmer", "Year Built": "<PERSON><PERSON><PERSON><PERSON>", "Keyword": "Stichwort", "-- Select Size --": "-- G<PERSON><PERSON><PERSON> auswählen --", "Page list properties layout": "Seitenlayout für Immobilienliste", "Display Type": "Anzeigetyp", "Add prefix Price in Property listing?": "Preis-Präfix in Immobilienliste hinzufügen?", "Open gallery when clicking Featured image on the Listing page?": "Galerie öffnen beim Klicken auf Hauptbild auf der Listenseite?", "Layout Map Position": "Layout-<PERSON><PERSON><PERSON>", "Layout Map Size": "Layout-Kartengröße", "Page Detail": "Seiten-Detail", "Config page detail property of your website": "Detail-Seite für Immobilien Ihrer Website konfigurieren", "Page single property layout": "Einzelimmobilien-Seitenlayout", "Config review for property": "Bewertung für Immobilie konfigurieren", "Enable review system for Property?": "Bewertungssystem für Immobilie aktivieren?", "Turn on the mode for reviewing property": "Modus für Immobilien-Bewertung aktivieren", "Agent config for property": "Agent-Konfiguration für Immobilie", "Property created by vendor must be approved by admin": "<PERSON> erstellte Immobilie muss vom Admin genehmigt werden", "Property information": "Immobilien-Informationen", "Property name": "Immobilien-Name", "All Bookings": "Alle Buchungen", "No items selected": "Keine Elemente ausgewählt", "Please select action": "Bitte Aktion auswählen", "Search results: \":s\"": "Suchergebnisse: \":s\"", "Enquiry Management": "Anfrage-Verwaltung", "Enquiry :name": "Anfrage :name", "All Replies": "Alle Antworten", "Replies": "Antworten", "Reply added": "Antwort hinzugefügt", "Reports :count": "Berichte :count", "Enquiry Reports": "Anfrage-Berichte", "Booking Reports": "Buchungs-Berichte", "Booking Statistic": "Buchungs-Statistik", "Credit Purchase Report :count": "Guthaben<PERSON><PERSON><PERSON><PERSON><PERSON>richt :count", "-- Bulk Actions --": "-- Massenaktionen --", "Mark as: :name": "Markieren als: :name", "DELETE booking": "Buchung LÖSCHEN", "Search by name or ID": "Nach Name oder ID suchen", "Service": "Service", "Payment Information": "Zahlungsinformationen", "Commission": "Provision", "by": "von", "Name:": "Name:", "Email:": "E-Mail:", "Phone:": "Telefon:", "Address:": "<PERSON><PERSON><PERSON>:", "Custom Requirement:": "Besondere Anforderung:", "Detail": "Detail", "Set Paid": "Als bezahlt markieren", "Email Preview": "E-Mail-Vorschau", "Booking ID: #": "Buchungs-ID: #", "All Enquiries": "Alle Anfragen", "DELETE Enquiry": "Anfrage LÖSCHEN", "Search by email": "Nach E-Mail suchen", "Enquiries": "Anfragen", "Notes:": "Notizen:", "Reply": "Antworten", "All Reply": "Alle Antworten", "Add Reply": "Antwort hinzufügen", "Client Message:": "Kunden-Nachricht:", "Content:": "Inhalt:", "Reply Content": "Antwort-Inhalt", "Add New": "<PERSON><PERSON>", "Recent updates": "Aktuelle Updates", "Bookings Statistic": "Buchungs-Statistik", "Filter:": "Filter:", "-- User Type --": "-- <PERSON><PERSON><PERSON><PERSON> --", "Customer User": "Ku<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vendor User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Detail statistics": "Detail-Statistiken", "Review": "Bewertung", "Review not enable": "Bewertung nicht aktiviert", "You need to make a booking or the Orders must be confirmed before writing a review": "Sie müssen eine Buchung vornehmen oder die Bestellungen müssen bestätigt werden, bevor Sie eine Bewertung schreiben können", "You cannot review your service": "Sie können Ihren eigenen Service nicht bewerten", "Review Title is required field": "Bewertungs-Titel ist ein Pflichtfeld", "Review Content is required field": "Bewertungs-Inhalt ist ein Pflichtfeld", "Review Content has at least 10 character": "Bewertungs-Inhalt muss mindestens 10 Zeichen haben", "Review success!": "Bewertung erfolgreich!", "Review success! Please wait for admin approved!": "Bewertung erfolgreich! Bitte warten Sie auf Admin-Genehmigung!", "Review error!": "Bewertungs-Fehler!", "Excellent": "Ausgezeichnet", "Very Good": "<PERSON><PERSON> gut", "Average": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Poor": "<PERSON><PERSON><PERSON><PERSON>", "Terrible": "<PERSON><PERSON><PERSON><PERSON>", "Review Advanced Settings": "Erweiterte Bewertungs-Einstellungen", "All Reviews": "Alle Bewertungen", " Approved ": " <PERSON><PERSON><PERSON><PERSON> ", " Pending ": " <PERSON><PERSON><PERSON><PERSON> ", " Spam ": " Spam ", " Move to Trash ": " In Papierkorb verschieben ", "-- Customer --": "-- Kunde --", "Search by title": "<PERSON><PERSON> T<PERSON>l suchen", "Approved": "<PERSON><PERSON><PERSON><PERSON>", "Spam": "Spam", "Trash": "Papierkorb", "Review Content": "Bewertungs-Inhalt", "In Response To": "Als Antwort auf", "Submitted On": "Eingereicht am", "More info": "Mehr Informationen", "View :name": ":name anzeigen", "Allow customer upload picture to review": "<PERSON><PERSON> er<PERSON>, Bilder zur Bewertung hochzuladen", "Based on": "Basierend auf", ":number reviews": ":number Bewertungen", ":number review": ":number Bewertung", "Showing :from - :to of :total total": "Zeige :from - :to von :total gesamt", "No Review": "<PERSON><PERSON>", "Write a review": "Eine Bewertung schreiben", "Review title is required": "Bewertungs-Titel ist erforderlich", "Review content": "Bewertungs-Inhalt", "Review content has at least 10 character": "Bewertungs-Inhalt muss mindestens 10 Zeichen haben", "Review rate": "Bewertungs-Rate", "Add photo": "Foto hinzufügen", "Leave a Review": "Eine Bewertung hinterlassen", "You must <a href='#login' data-toggle='modal' data-target='#login'>log in</a> to write review": "<PERSON><PERSON> mü<PERSON> sich <a href='#login' data-toggle='modal' data-target='#login'>an<PERSON><PERSON></a>, um eine Bewertung zu schreiben", "Sms Settings": "SMS-Einstellungen", "Social": "Social", "Forum": "Forum", "forum saved": "Forum gespeichert", "News Feed": "News-Feed", "Forums": "<PERSON>en", "Add new post": "Neuen Beitrag hinzufügen", "How are you feeling today?": "Wie fühlen Sie sich heute?", "Share": "Teilen", "Like": "Gefällt mir", "Comments": "Kommentare", "Space": "<PERSON><PERSON>", "Spaces": "<PERSON><PERSON><PERSON>", "Space Management": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Recovery Space Management": "Raum-Verwaltung wiederherstellen", "Add Space": "<PERSON><PERSON> hinz<PERSON>", "Add new Space": "Neuen Raum hinzufügen", "Edit Space": "<PERSON><PERSON> bearbeiten", "Space updated": "<PERSON><PERSON> aktualisiert", "Space created": "<PERSON><PERSON> er<PERSON>t", "Space: Form Search": "Raum: Suchformular", "Service Space": "Service Raum", "Space: List Items": "Raum: El<PERSON>e auflisten", "Space: Term Featured Box": "Raum: Begriff-Hervorhebungsbox", "Select term space": "Raum-Begriff auswählen", "Spaces Availability": "Raum-Verfügbarkeit", "Space not found": "Raum nicht gefunden", "Manage Spaces": "<PERSON><PERSON><PERSON> verwalten", "Recovery Spaces": "<PERSON><PERSON><PERSON> wied<PERSON>hers<PERSON>len", "Restore space success!": "Raum erfolgreich wiederhergestellt!", "Create Spaces": "<PERSON><PERSON><PERSON> er<PERSON>", "Edit Spaces": "<PERSON><PERSON><PERSON> bear<PERSON>", "Delete space success!": "Raum erfolgreich gelöscht!", "Space clone was successful": "Raum-Klonen war erfolgreich", ":count spaces found": ":count <PERSON><PERSON><PERSON> gefunden", ":count space found": ":count <PERSON><PERSON> gefunden", "Showing :from - :to of :total Spaces": "Zeige :from - :to von :total Räumen", "This space is not available at selected dates": "Dieser Raum ist an den ausgewählten Terminen nicht verfügbar", "You must to book a minimum of :number nights": "<PERSON>e müssen mindestens :number Nächte buchen", ":number Spaces": ":number Räume", ":number Space": ":number Raum", "All Spaces": "Alle Räume", "Manage Space": "<PERSON><PERSON> verwalten", "Space Settings": "Raum-Einstellungen", "Space Attributes": "Raum-Attribute", "Spaces Availability Calendar": "Raum-Verfügbarkeitskalender", "Showing :from - :to of :total spaces": "Zeige :from - :to von :total Räumen", "No spaces found": "<PERSON><PERSON> gefunden", "Add new space": "Neuen Raum hinzufügen", "View Space": "<PERSON><PERSON> anzeigen", "Space Featured": "<PERSON><PERSON> her<PERSON>", "No space found": "<PERSON><PERSON> gefunden", "Config review for space": "Bewertung für Raum konfigurieren", "Enable review system for Space?": "Bewertungssystem für Raum aktivieren?", "Turn on the mode for reviewing space": "Modus für Raum-Bewertung aktivieren", "Customer must book a space before writing a review?": "Kunde muss <PERSON> buchen, bevor er eine Bewertung schreibt?", "Config Booking for space": "Buchung für Raum konfigurieren", "Space by day": "<PERSON><PERSON> am <PERSON>", "Space by night": "Raum in der Nacht", "Vendor config for space": "Anbieter-Konfiguration für Raum", "Space created by vendor must be approved by admin": "<PERSON> erstellter Raum muss vom Admin genehmigt werden", "Disable space module?": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>?", "Disable space module": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Space Content": "Raum-Inhalt", "Space Price": "Raum-<PERSON><PERSON>", "Space Sale Price": "Raum-Verkaufspreis", "Space information": "Raum-<PERSON><PERSON>", "Space name": "Raum-Name", "Support Settings": "Support-Einstellungen", "Support Options": "Support-Optionen", "Ticket Options": "Ticket-Optionen", "Ticket Assign To": "<PERSON>icket zu<PERSON> an", "Supporter View Type": "Supporter-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Per user [Default]": "Pro Benutzer [Standard]", "Supporter see all": "Supporter si<PERSON><PERSON> alles", "Live Editor": "Live-Editor", "Template not found!": "Vorlage nicht gefunden!", "Template can\\'t export. Please try again": "Vorlage kann nicht exportiert werden. Bitte versuchen Sie es erneut", "Import template ' . @$dataInput['title'] . ' success!": "Import der Vorlage ' . @$dataInput['title'] . ' erfolgreich!", "Only support json file": "Nur JSON-<PERSON><PERSON> werden unterstützt", "Your template has been saved": "<PERSON><PERSON><PERSON> Vorlage wurde gespeichert", "Style Normal": "Stil Normal", "- Layout Normal: Background Color - get code in <a href=\"https://html-color-codes.info\" target=\"_blank\">https://html-color-codes.info</a>": "- Layout Normal: Hintergrundfarbe - Code abrufen unter <a href=\"https://html-color-codes.info\" target=\"_blank\">https://html-color-codes.info</a>", "- Layout 2&3 : Background Image Uploader": "- Layout 2&3: Hi<PERSON>grundbild-Uploader", "Client Feedback": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Column": "<PERSON>lt<PERSON>", "FAQ List": "FAQ-Liste", "Question": "Frage", "Answer": "Antwort", "Form Search All Service": "Suchformular für alle Services", "Title for :service": "Titel für :service", "Slider Carousel Ver 2": "Slider-Karussell Ver 2", "Background Video": "Hintergrund-Video", "- Layout Video: Youtube Url": "- Layout Video: YouTube-URL", "Title (using for slider ver 2)": "Titel (für Slider Ver 2 verwendet)", "Desc (using for slider ver 2)": "Beschreibung (für Slider Ver 2 verwendet)", "Hide form search service?": "Suchformular für Service ausblenden?", "How It Works": "Wie es funktioniert", "Image Uploader": "Bild-Uploader", "Style 5": "Stil 5", "List Featured Item": "Hervorgehobene Elemente auflisten", "Offer Block": "Angebots-Block", "Featured text": "Hervorgehobener Text", "Featured icon (find icon class in : https://icofont.com/icons)": "Hervorgehobenes Icon (Icon-Klasse finden unter: https://icofont.com/icons)", "Section": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Editor": "Editor", "Wrapper Class (opt)": "Wrapper-Klasse (optional)", "Padding": "Padding", "Video Player": "Video-Player", "Youtube link": "YouTube-Link", "Template": "Vorlage", "Edit Template:": "Vorlage bearbeiten:", "Create new template": "Neue Vorlage erstellen", "Template Name": "Vorlagen-Name", "Search for block...": "Nach Block suchen...", "Template Content": "Vorlagen-Inhalt", "You need to create the template at the Main-language tab first!": "Sie müssen die Vorlage zuerst im Hauptsprachen-Tab erstellen!", "Save Template": "Vorlage speichern", "Are you want to delete?": "Möchten Sie löschen?", "Import Template": "Vorlage importieren", "All Templates": "Alle Vorlagen", "Choose file": "<PERSON>i ausw<PERSON>hlen", "Import": "Importieren", "Template Management": "Vorlagen-Verwaltung", "Import new Template": "Neue Vorlage importieren", "Add new Template": "Neue Vorlage hinzufügen", "All templates": "Alle Vorlagen", "Export": "Exportieren", "Clone": "Klonen", "Last saved:": "Zuletzt gespeichert:", "Save Block": "Block speichern", "ADD LAYER": "EBENE HINZUFÜGEN", "Search block ...": "Block suchen ...", "LAYERS": "EBENEN", "Add layer": "<PERSON><PERSON><PERSON>", "Theme management": "Theme-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Theme Upload": "Theme-Upload", "Disable for demo mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Theme activated": "Theme aktiviert", "DEMO MODE: You are not allowed to do that": "DEMO-MODUS: <PERSON><PERSON> dürfen das nicht tun", "This theme does not have seeder class": "Dieses Theme hat keine Seeder-Klasse", "Demo data has been imported": "Demo-<PERSON>n wurden importiert", "Can not run data import": "Datenimport kann nicht ausgeführt werden", "Themes": "Themes", "All Themes": "Alle Themes", "Do you want to import all demo data?": "Möchten Sie alle Demo-Daten importieren?", "Import Demo Data": "Demo-Daten importieren", "Last run: :date": "<PERSON><PERSON><PERSON>: :date", "Activate": "Aktivieren", "Upload Theme": "Theme hochladen", "Select theme file": "Theme-<PERSON><PERSON>", "Select theme zip file:": "Theme-ZIP-<PERSON><PERSON> ausw<PERSON>en:", "Maximum file size is: ": "Maximale Dateigröße ist: ", "Upload Now": "Jetzt hochladen", "Tour": "Tour", "Attributes: :name": "Attribute: :name", "Term not found!": "Begriff nicht gefunden!", "Tours": "Touren", "Booking": "<PERSON><PERSON><PERSON>", "Tour Booking History": "Tour-Buchungsverlauf", "Tour Management": "Tour-<PERSON><PERSON><PERSON>tung", "Recovery Tour Management": "Tour-Verwal<PERSON>g wiederherstellen", "Add Tour": "Tour hinzufügen", "Edit Tour": "Tour bearbeiten", "Tour updated": "Tour aktualisiert", "Tour created": "Tour erstellt", "Select Category": "Kategorie auswählen", "Image Background": "Bild-Hintergrund", "Service Tour": "Service Tour", "Tour: Box Category": "Tour: Box-<PERSON><PERSON><PERSON>", "Tour: Form Search": "Tour: Suchformular", "Box Shadow": "Box-<PERSON><PERSON><PERSON>", "Slider Carousel Simple": "Slider<PERSON><PERSON><PERSON><PERSON>", "Tour: List Items": "Tour: Elemente auflisten", "Tours Availability": "Tour-Verfügbarkeit", "Tour not found": "Tour nicht gefunden", "Max guests: ": "<PERSON><PERSON>: ", "Manage Tours": "<PERSON><PERSON> verwalten", "Recovery Tours": "Touren wiederherstellen", "Restore tour success!": "Tour erfolgreich wiederhergestellt!", "Create Tours": "Touren erstellen", "Tour not found!": "Tour nicht gefunden!", "Edit Tours": "Touren bearbeiten", "Delete tour success!": "Tour erfolgreich gelöscht!", "Tour clone was successful": "Tour-Klonen war erfolgreich", ":count tours found": ":count <PERSON><PERSON> gefunden", ":count tour found": ":count Tour gefunden", "Showing :from - :to of :total Tours": "Zeige :from - :to von :total Touren", "Search for Tours": "<PERSON><PERSON> suchen", "There are :maxGuests guests available for your selected date": "Es sind :maxGuests Gäste für Ihr ausgewähltes Datum verfügbar", "This tour is not available at selected dates": "Diese Tour ist an den ausgewählten Terminen nicht verfügbar", "This tour is not open on your selected day": "Diese Tour ist an Ihrem ausgewählten Tag nicht geöffnet", "There are :numberGuestsCanBook guests available for your selected date": "Es sind :numberGuestsCanBook Gäste für Ihr ausgewähltes Datum verfügbar", "Not Rated": "<PERSON>cht bewertet", ":number Tours": ":number Touren", ":number Tour": ":number Tour", "Tour Type": "Tour-Typ", "Tour Category": "Tour-<PERSON><PERSON><PERSON>", "All Tours": "Alle Touren", "Booking Calendar": "Buchungskalender", "Manage Tour": "Tour verwalten", "Tour Settings": "Tour-Einstellungen", "Tour Attributes": "Tour-Attribute", "Tours Availability Calendar": "Tour-Verfügbarkeitskalender", "No tours found": "<PERSON>ine Touren gefunden", "Max Guest": "<PERSON><PERSON>", "Min": "Min", "Max": "Max", "Tour Booking Calendar": "Tour-Buchungskalender", "Tour Filters": "Tour-Filter", "Showing :from - :to of :total Tour(s)": "Zeige :from - :to von :total Tour(en)", "Tour Categories": "Tour-<PERSON><PERSON><PERSON>", "Add new tour": "Neue Tour hinzufügen", "View Tour": "Tour anzeigen", "Tour Information": "Tour-Informationen", "SEO": "SEO", "Tour Featured": "Tour hervorgehoben", "All Tour": "Alle Touren", "-- All Category --": "-- <PERSON><PERSON> --", "Config review for tour": "Bewertung für Tour konfigurieren", "Enable review system for Tour?": "Bewertungssystem für Tour aktivieren?", "Turn on the mode for reviewing tour": "Modus für Tour-Bewertung aktivieren", "Customer must book a tour before writing a review?": "Kunde muss Tour buchen, bevor er eine Bewertung schreibt?", "Does the review need approved by admin?": "Muss die Bewertung vom Admin genehmigt werden?", "Config buyer fees for tour": "Käufergebühren für Tour konfigurieren", "Vendor config for tour": "Anbieter-Konfiguration für Tour", "Tour create by vendor must be approved by admin?": "<PERSON>ter erstellte Tour muss vom Admin genehmigt werden?", "Disable tour module?": "Tour-<PERSON><PERSON><PERSON>?", "Disable tour module": "Tour-<PERSON><PERSON><PERSON>", "Fixed dates": "Feste Termine", "Enable Fixed Date": "Festes Datum aktivieren", "Start Date": "Startdatum", "Last Booking Date": "Letztes Buchungsdatum", "Open Hours": "Öffnungszeiten", "Enable Open Hours": "Öffnungszeiten aktivieren", "Enable?": "Aktivieren?", "Day of Week": "Wochentag", "Open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tuesday": "Dienstag", "Wednesday": "Mittwoch", "Thursday": "Don<PERSON><PERSON>", "Friday": "Freitag", "Saturday": "Samstag", "Itinerary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title - Desc": "Titel - Beschreibung", "Title: Day 1": "Titel: Tag 1", "Desc: TP. HCM City": "Beschreibung: <PERSON><PERSON><PERSON><PERSON>Minh-Stadt", "Tour Price": "Tour-Preis", "Tour Sale Price": "Tour-Verkaufspreis", "Person Types": "Personentypen", "Enable Person Types": "Personentypen aktivieren", "Person Type": "Personentyp", "Eg: Adults": "Z.B.: Erwachsene", "Minimum per booking": "Minimum pro Buchung", "Maximum per booking": "Maximum pro Buchung", "per 1 item": "pro 1 Element", "Tour Content": "Tour-Inhalt", "Tour Min People": "Tour Min. <PERSON>en", "Tour Max People": "Tour Max. Personen", "Tour Locations": "Tour-<PERSON><PERSON><PERSON>", "Real tour address": "<PERSON>chte Tour-Adresse", "Tour information": "Tour-Informationen", "Tour name": "Tour-Name", "Discounts:": "Rabatte:", "from :from guests": "ab :from Gästen", ":from - :to guests": ":from - :to Gäste", "Tracking Report": "Tracking-Bericht", "Select at leas 1 item!": "Wählen Sie mindestens 1 Element!", "Deleted!": "Gelöscht!", "Updated successfully!": "Erfolgreich aktualisiert!", "Tracking": "Tracking", "Tracking Settings": "Tracking-Einstellungen", "-- Event Type --": "-- Event-Typ --", "Phone Click": "Telefon-Klick", "Website Click": "Website-Klick", "Enquiry Click": "Anfrage-Klick", "Email Ads Click": "E-Mail-Anzeigen-Klick", "Ads Name": "Anzeigen-Name", "-- Service Type --": "-- Service-Typ --", "Campaign": "<PERSON><PERSON><PERSON><PERSON>", "Service ID": "Service-ID", "Lang": "<PERSON><PERSON><PERSON>", "Ip": "IP", "Payout ID": "Auszahlungs-ID", "CPC": "CPC", "Tracking System": "Tracking-System", "Config tracking system option": "Tracking-System-Option konfigurieren", "Enable Tracking": "Tracking aktivieren", "Yes,please enable it": "<PERSON><PERSON>, bitte aktivieren", "Do not track these IP address": "Diese IP-Adressen nicht verfolgen", "Example: *************, **************": "Beispiel: *************, **************", "User Plans": "Benutzer-Pläne", "User Plan Management": "Benutzer-Plan-Verwaltung", "Edit user plan": "Benutzer-Plan bearbeiten", "Plan saved": "Plan gespeichert", "Plan Report": "Plan-Bericht", "Plan request management": "Plan-Anfrage-Verwaltung", "Users": "<PERSON><PERSON><PERSON>", "Select at lease 1 item!": "Wählen Sie mindestens 1 Element!", "Deleted successfully!": "Erfolgreich gelöscht!", "Roles": "<PERSON><PERSON>", "Role updated": "Rolle aktualisiert", "Edit Role": "<PERSON><PERSON> bearbeiten", "DEMO Mode: You can not do this": "DEMO-Modus: <PERSON><PERSON> können das nicht tun", "Role created": "<PERSON><PERSON> erste<PERSON>t", "Role Management": "Rollen-Verwaltung", "Verify Configs": "Verifizierungs-Konfigurationen", "Field not found": "Feld nicht gefunden", "Edit field: :name": "Feld bearbeiten: :name", "Field created": "<PERSON><PERSON> er<PERSON>", "Field saved": "<PERSON><PERSON> g<PERSON>", "Permission Matrix": "Berechtigungs-Matrix", "Permission Matrix updated": "Berechtigungs-Matrix aktualisiert", "Subscribers": "Abonnenten", "Edit: :email": "Bearbeiten: :email", "Email exists": "E-Mail existiert", "Subscriber updated": "Abonnent aktualisiert", "Edit User: #:id": "Benutzer bearbeiten: #:id", "DEMO MODE: You can not change password!": "DEMO-MODUS: <PERSON><PERSON> können das Passwort nicht ändern!", "Your current password does not matches with the password you provided. Please try again.": "Ihr aktuelles Passwort stimmt nicht mit dem angegebenen Passwort überein. Bitte versuchen Si<PERSON> es erneut.", "Password updated!": "Passwort aktualisiert!", "Display name is a required field": "Anzeigename ist ein Pflichtfeld", "User updated": "Benutzer aktualisiert", "User created": "<PERSON>utz<PERSON> er<PERSON>llt", "Verify email successfully!": "E-Mail erfolgreich verifiziert!", "Verify email cancel!": "E-Mail-Verifizierung abgebrochen!", "Verification Request": "Verifizierungsanfrage", "Verify request: :email": "Anfrage verifizieren: :email", "User not found": "Benutzer nicht gefunden", "No verification field found": "<PERSON><PERSON> Verifizierungsfeld gefunden", "Updated": "<PERSON>ktual<PERSON><PERSON>", "Add Credit": "<PERSON><PERSON><PERSON><PERSON>", "Add credit for :name": "Guthaben für :name hinz<PERSON><PERSON>gen", ":amount credit added": ":amount <PERSON><PERSON><PERSON><PERSON> hinzugefügt", "Credit purchase report": "Guthaben-Kaufbericht", "Phone is required field": "Telefon ist ein Pflichtfeld", "Invoice": "<PERSON><PERSON><PERSON><PERSON>", ":name send you message: :message": ":name hat Ihnen eine Nachricht gesendet: :message", ":name send you file": ":name hat Ihnen eine Date<PERSON> gesendet", "New Password cannot be same as your current password. Please choose a different password.": "Das neue Passwort kann nicht dasselbe wie Ihr aktuelles Passwort sein. Bitte wählen Sie ein anderes Passwort.", "Password changed successfully !": "Passwort erfolgreich geändert!", "Pricing Packages": "Preispakete", "My Plan": "Mein <PERSON>", "My plans": "<PERSON><PERSON>", "This plan is not suitable for your role.": "Dieser Plan ist nicht für Ihre Rolle geeignet.", "This plan doesn't have annual pricing": "Dieser Plan hat keine jährliche Preisgestaltung", "Please select payment gateway": "Bitte Zahlungsgateway auswählen", "Purchased user package successfully": "Benutzerpaket erfolgreich gekauft", ":name - reviews from guests": ":name - <PERSON><PERSON><PERSON><PERSON>", "Reviews from guests": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", ":name - :type": ":name - :type", ":type by :first_name": ":type von :first_name", "Two Factor Authentication": "Zwei-Faktor-Authentifizierung", "Profile": "Profil", "The User name field is required.": "Das Benutzername-Feld ist erforderlich.", "Thank you for subscribing": "Vielen Dank für Ihr Abonnement", "You are already subscribed": "Sie sind bereits abonniert", "You have just done the become vendor request, please wait for the Admin's approved": "Sie haben gerade die Anbieter-An<PERSON><PERSON> geste<PERSON>, bitte warten Sie auf die Genehmigung des Administrators", "Request vendor success!": "Anbieter-Anfrage erfolgreich!", "Error. You can\\'t permanently delete": "Fehler. Si<PERSON> können nicht dauerhaft löschen", "Wishlist": "Wunschliste", "Service ID is required": "Service-ID ist erforderlich", "Service type is required": "Service-Typ ist erforderlich", "Delete fail!": "Löschen fehlgeschlagen!", "Verification": "Verifizierung", "Update Verification Data": "Verifizierungsdaten aktualisieren", "The :name is required": "Das :name ist er<PERSON><PERSON><PERSON>", "The :name path is required": "Der :name-<PERSON><PERSON><PERSON> ist er<PERSON>lich", "Verification data saved. Please wait for admin approval": "Verifizierungsdaten gespeichert. Bitte warten Sie auf Admin-Genehmigung", "Verify code do not match": "Verifizierungscode stimmt nicht überein", "Wallet": "Wallet", "Deposit option is not valid": "Einzahlungsoption ist nicht gültig", "Deposit option amount is not valid": "Einzahlungsoptionsbetrag ist nicht gültig", "Deposit option credit is not valid": "Einzahlungsoptionsguthaben ist nicht gültig", "[:site_name] We updated your verification data": "[:site_name] Wir haben Ihre Verifizierungsdaten aktualisiert", "[:site_name] Verify Register": "[:site_name] Registrierung verifizieren", "Verify Email Address": "E-Mail-Adresse verifizieren", "[:site_name] Permanently Delete Account": "[:site_name] Konto dauerhaft löschen", "[:site_name] An user submitted verification data": "[:site_name] Ein Benutzer hat Verifizierungsdaten übermittelt", "Vendor Registration Approved": "Anbieter-Registrierung genehmigt", "New Vendor Registration": "Neue Anbieter-Registrierung", "Business Name": "Firmenname", "Address 2": "Adresse 2", "State": "Bundesland", "Zip Code": "<PERSON><PERSON><PERSON><PERSON>", "Your upgrade request has approved already": "Ihre Upgrade-<PERSON><PERSON><PERSON> wurde bereits genehmigt", "Your has created a plan request": "<PERSON>e haben eine Plan-Anfrage erstellt", " has created a plan request": " hat eine Plan-<PERSON><PERSON><PERSON> erste<PERSON>t", ":name has requested to become a vendor": ":name hat bean<PERSON><PERSON>, <PERSON><PERSON><PERSON> zu werden", " has been registered": " wurde registriert", ":name has requested a Credit Purchase : :amount": ":name hat einen <PERSON>-<PERSON><PERSON> beantragt: :amount", "Administrator has approved your Credit amount": "Administrator hat <PERSON><PERSON>-<PERSON><PERSON>", "Your plan request has been approved": "Ihre Plan-An<PERSON><PERSON> wurde genehmigt", "Your plan request has been cancelled": "Ihre Plan-Anfrage wurde storniert", " plan request has been approved": " Plan-<PERSON><PERSON><PERSON> wurde gene<PERSON>t", " plan request has been cancelled": " Plan-<PERSON><PERSON><PERSON> wurde storniert", "Your account information was verified": "Ihre Kontoinformationen wurden verifiziert", ":name has asked for verification": ":name hat um Verifizierung gebeten", "Someone": "<PERSON><PERSON>", "You have just gotten a new Subscriber": "Sie haben gerade einen neuen Abonnenten erhalten", ":duration day": ":duration Tag", ":duration days": ":duration Tage", ":duration week": ":duration Woche", ":duration weeks": ":duration Wochen", ":duration month": ":duration Monat", ":duration months": ":duration Monate", ":duration year": ":duration Jahr", ":duration years": ":duration Jahre", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "year": "<PERSON><PERSON><PERSON>", "Users :count": "Ben<PERSON>er :count", "Role Manager": "<PERSON><PERSON>-Manager", "Upgrade Request :count": "Upgrade-Anfrage :count", "Verification Request :count": "Verifizierungsanfrage :count", "User Plans :count": "Benutzer-Pläne :count", "Plan Request :count": "Plan-Anfrage :count", "My Wallet": "<PERSON><PERSON>", "Verifications": "Verifizierungen", "Messages :count": "Nachrichten :count", "2F Authentication": "2F-Authentifizierung", "My Plans": "<PERSON><PERSON>", "Wallet Settings": "Wallet-Einstellungen", "User Settings": "Benutzer-Einstellungen", "User Plans Settings": "Benutzer-Plan-Einstellungen", "User Info": "Benutzer-Info", "Business name": "Firmenname", "E-mail": "E-Mail", "User name": "<PERSON><PERSON><PERSON><PERSON>", "Phone Number": "Telefonnummer", "Birthday": "Geburtstag", "Address Line 1": "Adresszeile 1", "Address Line 2": "Adresszeile 2", "Biographical": "Biografisch", "Email Verified?": "E-Mail verifiziert?", "Vendor Commission Type": "Anbieter-Provisionstyp", "Disable Commission": "Provision deaktivieren", "Vendor commission value": "Anbieter-Provisionswert", "Avatar": "Avatar", "Export to excel": "Nach Excel exportieren", "Verified": "Verifiziert", "Not Verified": "Nicht verifiziert", "Verify email": "E-Mail verifizieren", "Email verified": "E-Mail verifiziert", "Old Password": "Altes Passwort", "New password": "Neues Passwort", "Re-Password": "Passwort wiederholen", "Permission Content": "Berechtigungs-Inhalt", "Add new permission": "Neue Berechtigung hinzufügen", "All Permission": "Alle Berechtigungen", "Add Plan": "Plan hinzufügen", "Plan Content": "Plan-Inhalt", "name": "Name", "For Role": "<PERSON><PERSON><PERSON>", "Free": "<PERSON><PERSON><PERSON>", "Annual Price": "Jahrespreis", "Duration Type": "<PERSON><PERSON>-<PERSON><PERSON>", "Week": "<PERSON><PERSON><PERSON>", "Max Services": "Max. Services", "Unlimited": "Unbegrenzt", "How many publish services user can post": "Wie viele veröffentlichte Services der Benutzer posten kann", "ID": "ID", "-- Select Employer --": "-- Arbeitgeber auswählen --", " All Plan ": " Alle Pläne ", "Plan ID": "Plan-ID", "Plan Name": "Plan-Name", "Expiry": "<PERSON><PERSON><PERSON>", "Used/Total": "Verwendet/Gesamt", "Expired": "Abgelaufen", "Renew": "<PERSON><PERSON><PERSON><PERSON>", "Mark as completed": "Als abgeschlossen markieren", "Mark as cancelled": "Als storniert markieren", "-- Status --": "-- Status --", "-- User --": "-- <PERSON><PERSON><PERSON> --", "Purchase logs": "Kauf-Lo<PERSON>", "Plan": "Plan", "Name: :name": "Name: :name", "Duration:  :duration_text": "Dauer: :duration_text", "Role Content": "Rollen-Inhalt", "Role Name": "<PERSON>en-Name", "Role Code": "Rollen-Code", "Should be unique and letters only": "Sollte eindeutig und nur Buchstaben sein", "Add new role": "Neue Rolle hinzufügen", "All Roles": "Alle Rollen", "Manage Fields": "<PERSON><PERSON> verwalten", "Add new field": "Neues Feld hi<PERSON>ufügen", "All Fields": "<PERSON><PERSON> Felder", "Icon": "Icon", "For roles": "<PERSON><PERSON><PERSON>", "Required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Edit Field: :name": "Feld bearbeiten: :name", "Edit verification field": "Verifizierungsfeld bearbeiten", "Field ID": "Feld-ID", "Field ID ": "Feld-ID ", "Must be unique. Only accept letter and number, dash, underscore, without space": "Muss eindeutig sein. Akzeptiert nur Buchstaben und Zahlen, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ohne Leerzeichen", "Please enter field id and make sure it unique": "Bitte geben Sie die Feld-ID ein und stellen Si<PERSON> sicher, dass sie eindeutig ist", "Field Name": "Feld-Name", "Please enter field name": "<PERSON><PERSON> Feld-Name eingeben", "File attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Multi files attachment": "<PERSON><PERSON><PERSON>", "Please enter field type": "<PERSON>te Feld-<PERSON><PERSON> e<PERSON>ben", "For Roles?": "<PERSON><PERSON><PERSON>?", "Please enter roles": "Bitte Rollen eingeben", "Is Required?": "Ist er<PERSON>?", "Icon code": "Icon-Code", "Eg: fa fa-phone": "Z.B.: fa fa-phone", "User Plans Options": "Benutzer-Plan-Optionen", "Config user plans page": "Benutzer-Plan-Seite konfigurieren", "Enable User Plans": "Benutzer-Pläne aktivieren", "Page Title": "Seitentitel", "Page Sub Title": "Seiten-Untertitel", "Sale Of Text": "Verkaufstext", "Enable Multi User Plans": "Me<PERSON>ere <PERSON>utzer-Pläne aktivieren", "Plan Request options": "Plan-Anfrage-Optionen", "Content email send to Customer or Administrator.": "Inhalts-E-Mail an Kunde oder Administrator senden.", "New request plan": "Neue Plan-Anfrage", "Enable send email to Administrator?": "E-Mail an Administrator senden aktivieren?", "Subject": "<PERSON><PERSON><PERSON>", "Enable send email to customer?": "E-Mail an Kunde senden aktivieren?", "Update request plan": "Plan-Anfrage aktualisieren", "Register Options": "Registrierungs-Optionen", "Config register option": "Registrierungs-Option konfigurieren", "Disable Registration?": "Registrierung deaktivieren?", "User Register Default Role": "Standard-Rolle für Benutzer-Registrierung", "Inbox System": "Posteingang-System", "Config inbox option": "Posteingang-Option konfigurieren", "Allow customer can send message to the vendor on detail page": "<PERSON><PERSON> erlauben, <PERSON>ch<PERSON><PERSON> an Anbieter auf der Detailseite zu senden", "Google reCapcha Options": "Google reCAPTCHA-Optionen", "Config google recapcha for system": "Google reCAPTCHA für System konfigurieren", "Enable reCapcha Login Form": "reCAPTCHA für Anmeldeformular aktivieren", "Turn on the mode for login form": "Modus für Anmeldeformular aktivieren", "Enable reCapcha Register Form": "reCAPTCHA für Registrierungsformular aktivieren", "Turn on the mode for register form": "Modus für Registrierungsformular aktivieren", "Disable verification feature?": "Verifizierungsfunktion deaktivieren?", "Disable verification feature": "Verifizierungsfunktion deaktivieren", "When two factor authentication feature is enabled, the user is required to input a six digit numeric token during the authentication process. This token is generated using a time-based one-time password (TOTP) that can be retrieved from any TOTP compatible mobile authentication application such as Google Authenticator.": "Wenn die Zwei-Faktor-Authentifizierung aktiviert ist, muss der Benutzer während des Authentifizierungsprozesses einen sechsstelligen numerischen Token eingeben. Dieser Token wird mit einem zeitbasierten Einmalpasswort (TOTP) generiert, das von jeder TOTP-kompatiblen mobilen Authentifizierungs-App wie Google Authenticator abgerufen werden kann.", "Content Email User Registered": "Inhalts-E-Mail Benutzer registriert", "Content email send to Customer or Administrator when user registered.": "Inhalts-E-Mail an Kunde oder Administrator senden, wenn Benutzer registriert.", "Email to customer content": "E-Mail an Kunden-Inhalt", "Content Email User Verify Registered": "Inhalts-E-Mail Benutzer-Verifizierung registriert", "Content email verify send to Customer when user registered.": "Inhalts-E-Mail-Verifizierung an Kunde senden, wenn Benutzer registriert.", "Enable must verify email when customer registered ?": "E-Mail-Verifizierung bei Kunden-Registrierung erforderlich?", "Content Email User Forgot Password": "Inhalts-E-Mail Benutzer Passwort vergessen", "Disable Wallet module?": "Wallet-<PERSON><PERSON><PERSON>?", "Disable wallet module": "Wallet-<PERSON><PERSON><PERSON>", "Credit Options": "Guthaben-Optionen", "Credit exchange rate": "Guthaben-Wechselkurs", "Exchange rate will be used in checkout page. Example: Credit * Exchange rate = Money": "Wechselkurs wird auf der Checkout-Seite verwendet. Beispiel: Guthaben * Wechselkurs = Geld", "Deposit Options": "Einzahlungs-Optionen", "Deposit type": "Einzahlungstyp", "User input": "Benutzereingabe", "Select from lists": "Aus Listen auswählen", "Deposit rate": "Einzahlungsrate", "Example: Money * Deposit rate = Credit": "Beispiel: Geld * Einzahlungsrate = Guthaben", "Deposit lists": "Einzahlungslisten", "Earn credit": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "All amount will be in main currency": "Alle Beträge werden in der Hauptwährung angegeben", "New Credit Purchase Email Template": "Neue Guthaben-Kauf-E-Mail-Vorlage", "Email for Admin": "E-Mail für Admin", "Email for Customer": "E-Mail für Kunde", "Credit Purchase Updated Template": "Guthaben-Kauf-Aktualisierungs-Vorlage", "Permanently delete account": "Konto dauerhaft löschen", "Permanently delete account will delete all services of that user and that user": "Dauerhaftes Löschen des Kontos löscht alle Services dieses Benutzers und den Benutzer", "Content confirm": "Inhalt bestätigen", "Content Email Permanently delete account": "Inhalts-E-Mail Konto dauerhaft löschen", "Content email verify send when user permanently deleted.": "Inhalts-E-Mail-Verifizierung senden, wenn Benutzer dauerhaft gelöscht.", "To customer": "An Kunde", "To admin": "An Admin", "Vendor Requests": "Anbieter-Anfragen", "Role request": "Rollen-Anfrage", "Date request": "Anfrage-Datum", "Date approved": "Genehmigungs-Datum", "Approved By": "<PERSON><PERSON><PERSON><PERSON> von", "Approve": "<PERSON><PERSON><PERSON><PERSON>", "Data": "Daten", "Information": "Information", "Mark as verified": "Als verifiziert markieren", "Verification Requests": "Verifizierungsanfragen", "All Verification": "Alle Verifizierungen", "View Verification": "Verifizierung anzeigen", "View request": "An<PERSON>ge anzeigen", "Add credit": "<PERSON><PERSON><PERSON><PERSON>", "Balance": "<PERSON><PERSON><PERSON><PERSON>", "Credit Amount": "Guthaben-Betrag", "Add now": "Jetzt hinzufügen", "Credit Purchase Report": "Guthaben-<PERSON><PERSON>-<PERSON>t", "Hello :name": "Hallo :name", "You are receiving this email because we updated your vendor verification data.": "Sie erhalten diese E-Mail, weil wir Ihre Anbieter-Verifizierungsdaten aktualisiert haben.", "Not verified": "Nicht verifiziert", "You can check your information here:": "Sie können Ihre Informationen hier überprüfen:", "View verification data": "Verifizierungsdaten anzeigen", "Regards": "Mit freundlichen Grüßen", "An user has been submit their verification data.": "Ein Benutzer hat seine Verifizierungsdaten übermittelt.", "You can approved the request here:": "Sie können die Anfrage hier genehmigen:", "You are receiving this email because we approved your vendor registration request.": "Sie erhalten diese E-Mail, weil wir Ihre Anbieter-Registrierungsanfrage genehmigt haben.", "You can check your dashboard here:": "Sie können Ihr Dashboard hier überprüfen:", "View dashboard": "Dashboard anzeigen", "Add new subscriber": "Neuen Abonnenten hinzufügen", "Subscriber Info": "Abonnenten-Info", "Add Subscriber": "Abonnent hinzufügen", "Search by name or email": "Nach Name oder E-Mail suchen", "Payout Management": "Auszahlungs-Verwaltung", "Vendor Plans": "Anbieter-Pläne", "Plan created": "Plan erstellt", "Vendor plan updated": "Anbieter-Plan aktualisiert", "List Vendor": "An<PERSON><PERSON> auflisten", "Vendor Register Form": "Anbieter-Registrierungsformular", "Enquiry Report": "Anfrage-Bericht", "Enquiry not found!": "Anfrage nicht gefunden!", "Payouts Management": "Auszahlungs-Verwaltung", "Vendor dashboard": "Anbieter-Dashboard", "Payouts": "Auszahlungen", "Your account information has been saved": "Ihre Kontoinformationen wurden gespeichert", "Sorry! No method available at the moment": "Entschuldigung! Derzeit ist keine Methode verfügbar", "You does not select payout method or you need to enter account info for that method": "Sie haben keine Auszahlungsmethode ausgewählt oder müssen Kontoinformationen für diese Methode eingeben", "You don not have enough :amount for payout": "Sie haben nicht genug :amount für die Auszahlung", "Minimum amount to pay is :amount": "Mindestbetrag für Auszahlung ist :amount", "Payout request has been created": "Auszahlungsanfrage wurde erstellt", "Can not create vendor message": "Kann keine Anbieter-Nachricht erstellen", "Team members": "Team-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Member does not exists": "Mit<PERSON>ed existiert nicht", "You can not add yourself": "<PERSON>e können sich nicht selbst hinzufügen", "Request exists": "<PERSON><PERSON><PERSON> exist<PERSON>t", "Request created": "<PERSON><PERSON><PERSON> erste<PERSON>t", "A payout request has been updated": "Eine Auszahlungsanfrage wurde aktualisiert", "Your payout request has been updated": "I<PERSON>e Auszahlungsanfrage wurde aktualisiert", "A vendor has been submitted a payout request": "Ein Anbieter hat eine Auszahlungsanfrage eingereicht", "Your payout request has been submitted": "I<PERSON>e Auszahlungsanfrage wurde eingereicht", "A payout request has been deleted": "Eine Auszahlungsanfrage wurde gelöscht", "Your payout request has been deleted": "I<PERSON>e Auszahlungsanfrage wurde gelöscht", "A payout request has been rejected": "Eine Auszahlungsanfrage wurde abgelehnt", "Your payout request has been rejected": "<PERSON><PERSON>e Auszahlungsanfrage wurde abgelehnt", "Request join team": "Anfrage Team beitreten", "Administrator has :action your PAYOUT request": "Administrator hat <PERSON>hre AUSZAHLUNGS-Anfrage :action", ":name has sent a Payout request": ":name hat eine Auszahlungsanfrage gesendet", "Initial": "Initial", "Rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vendor Plan Meta": "Anbieter-Plan-Meta", "User upgrade request": "Benutzer-Upgrade-Anfrage", "Payouts :count": "Auszahlungen :count", "Teams": "Teams", "Vendor Settings": "Anbieter-Einstellungen", "Payout request management": "Auszahlungsanfrage-Verwaltung", "With selected:": "Mit ausgewählten:", "Bulk action": "Massenaktion", "Search by payout id": "<PERSON><PERSON>-ID suchen", "Payout Method": "Auszahlungsmethode", "To admin:": "An Admin:", "To vendor:": "An Anbieter:", ":name to :info": ":name an :info", "Payout request bulk action": "Auszahlungsanfrage-Massenaktion", "Pay date": "Zahlungsdatum", "YYYY/MM/DD": "JJJJ/MM/TT", "Note to vendor": "<PERSON>iz an <PERSON>", "Please select at lease one item": "Bitte wählen Sie mindestens ein Element", "Do you want to delete those items?": "Möchten Sie diese Elemente löschen?", "Status is empty": "Status ist leer", "Team Members": "Team-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Change your config vendor team members": "<PERSON><PERSON><PERSON>-Team-Mitglieder-Konfiguration ändern", "Team Member enable?": "Team-<PERSON><PERSON><PERSON><PERSON> aktivieren?", "Auto-approve team member request?": "Team-Mitglied-Anfrage automatisch genehmigen?", "Terms & Conditions": "Allgemeine Geschäftsbedingungen", "Config Vendor": "Anbieter konfigurieren", "Change your config vendor system": "Ihr Anbieter-System konfigurieren", "Vendor Enable?": "Anbieter aktivieren?", "Example value : 10 or 10.5": "Beispielwert: 10 oder 10,5", "Example: 10% commssion. Vendor get 90%, Admin get 10%": "Beispiel: 10% Provision. Anbieter erhält 90%, <PERSON><PERSON> erh<PERSON> 10%", "Vendor Register": "Anbieter-Registrierung", "Vendor Auto Approved?": "Anbieter automatisch genehmigen?", "Vendor Role": "Anbieter-Rolle", "Vendor Profile": "Anbieter-Profil", "Show vendor email in profile?": "Anbieter-E-Mail im Profil anzeigen?", "Show vendor phone in profile?": "Anbieter-Telefon im Profil anzeigen?", "Payout Options": "Auszahlungs-Optionen", "Disable Payout Module?": "Auszahlungs-<PERSON><PERSON><PERSON>?", "Booking Status Conditions": "Buchungsstatus-Bedingungen", "Select booking status will be use for calculate payout of vendor": "Buchungsstatus auswählen, der für die Berechnung der Anbieter-Auszahlung verwendet wird", "Payout Methods": "Auszahlungsmethoden", "Eg: bank_transfer": "Z.B.: bank_transfer", "Minimum to pay": "Mindestbetrag für Auszahlung", "Content Email Vendor Registered": "Inhalts-E-Mail Anbieter registriert", "Content email send to Vendor or Administrator when user registered.": "Inhalts-E-Mail an Anbieter oder Administrator senden, wenn Benutzer registriert.", "Email to vendor content": "E-Mail an Anbieter-Inhalt", "Your payout request has been submitted:": "Ihre Auszahlungsanfrage wurde eingereicht:", "Your payout request has been updated:": "Ihre Auszahlungsanfrage wurde aktualisiert:", "Your payout request has been rejected:": "Ihre Auszahlungsanfrage wurde abgelehnt:", "Status:": "Status:", "Pay date:": "Zahlungsdatum:", "Note to vendor:": "Notiz an <PERSON>:", "Payout information:": "Auszahlungsinformationen:", "Payout ID:": "Auszahlungs-ID:", "Amount: ": "Betrag: ", "Payout method: ": "Auszahlungsmethode: ", "Note to admin: ": "Notiz an Admin: ", "Created at: ": "Erstellt am: ", "You can check your payout request here:": "Sie können Ihre Auszahlungsanfrage hier überprüfen:", "View payouts": "Auszahlungen anzeigen", "Hello administrator": "Hallo Administrator", "A vendor has been submitted a payout request:": "Ein Anbieter hat eine Auszahlungsanfrage eingereicht:", "A payout request has been updated:": "Eine Auszahlungsanfrage wurde aktualisiert:", "A payout request has been rejected:": "Eine Auszahlungsanfrage wurde abgelehnt:", "Vendor: ": "Anbieter: ", "You can check all payout request here:": "Sie können alle Auszahlungsanfragen hier überprüfen:", "Manage payouts": "Auszahlungen verwalten", "Member Since :time": "Mit<PERSON>ed seit :time", "View Profile": "<PERSON><PERSON> anzeigen", "All Booking": "Alle Buchungen", "Order Date": "Bestelldatum", "Execution Time": "Ausführungszeit", "Payment Detail": "Zahlungsdetails", "No Booking History": "<PERSON><PERSON>", "Service Info": "Service-Info", "Customer Info": "Kunden-Info", "Vendor Payouts": "Anbieter-Auszahlungen", "No payout methods available. Please contact administrator": "<PERSON><PERSON>ahlungsmethoden verfügbar. Bitte kontaktieren Sie den Administrator", "Payout history": "Auszahlungshistorie", "#": "#", "Date Request": "Anfrage-Datum", "Notes": "Notizen", "Date Processed": "Verarbeitungsdatum", "Create request": "<PERSON><PERSON><PERSON> er<PERSON>", "Balance: ": "Guthaben: ", "Your balance is zero": "<PERSON>hr G<PERSON>aben ist null", "Create payout request": "Auszahlungsanfrage erstellen", "Available for payout": "Verfügbar für Auszahlung", "Method": "<PERSON>e", "Minimum: :amount": "Minimum: :amount", "Note to admin": "Notiz an Admin", "Send request": "<PERSON><PERSON><PERSON> senden", "Setup your payment accounts": "Ihre Zahlungskonten einrichten", "Setup accounts": "Konten einrichten", "To create payout request, please setup your payment account first": "Um eine Auszahlungsanfrage zu erstellen, richten <PERSON> bitte zuerst Ihr Zahlungskonto ein", "Setup payout accounts": "Auszahlungskonten einrichten", "Your account": "<PERSON><PERSON>", "Your account info": "<PERSON><PERSON>e Kontoinformationen", "Vendor Teams": "Anbieter-Teams", "As an author, you can add other users to your team. People on your team will be able to manage your services.": "Als Autor können Sie andere Benutzer zu Ihrem Team hinzufügen. Personen in Ihrem Team können Ihre Services verwalten.", "Add someone to your team:": "<PERSON><PERSON><PERSON> zu Ihrem Team hinzufügen:", "Permissions": "Berechtigungen", "Add": "Hinzufügen", "Users on your team": "<PERSON><PERSON><PERSON> in Ihrem Team", "Send email": "E-Mail senden", "Enable Two Checkout?": "Two Checkout aktivieren?", "Two Checkout": "Two Checkout", "Account Number": "<PERSON><PERSON><PERSON><PERSON>", "Secret Word": "Geheimes Wort", "Gateway 2Checkout": "Gateway 2Checkout", "Gateway 2Checkout is one of the best payment Gateway to accept online payments from buyers around the world which allow your customers to make purchases in many payment methods, 15 languages, 87 currencies, and more than 200 markets in the world.": "Gateway 2Checkout ist eines der besten Zahlungs-Gateways, um Online-Zahlungen von Käufern auf der ganzen Welt zu akzeptieren, das Ihren Kunden ermöglicht, Käufe in vielen Zahlungsmethoden, 15 Sprachen, 87 Währungen und mehr als 200 Märkten weltweit zu tätigen.", "Expiration Month": "Ablaufmonat", "Expiration Year": "<PERSON><PERSON>uf<PERSON><PERSON>", "Write a ": "Schreiben Si<PERSON> eine ", "OpenAI Settings": "OpenAI-Einstellungen", "API Key": "API-Schlüssel", "Model Name": "Modell-Name", "Magic text generator": "Magischer Textgenerator", "Keywords": "Stichwörter", "Some basic information or keywords": "Einige grundlegende Informationen oder Stichwörter", "Generate": "<PERSON><PERSON><PERSON>", "Use this content": "Diesen Inhalt verwenden", "Booking status not valid": "Buchungsstatus nicht gültig", "Ticket not found": "Ticket nicht gefunden", "This ticket does not belong to your events": "<PERSON><PERSON>et gehört nicht zu <PERSON> Events", "Ticket already scanned at :time": "Ticket bereits gescannt um :time", "Ticket scan success": "Ticket-<PERSON><PERSON>", "Ticket ID": "Ticket-ID", "Show QR Code at the counter": "QR-Code am Schalter anzeigen", "QR Code scanned at: :time": "QR-Code gescannt um: :time", "Manage category": "<PERSON><PERSON><PERSON> ver<PERSON>", "Edit category": "<PERSON><PERSON><PERSON> bear<PERSON>", "Ticket Management": "Ticket-Verwaltung", "Support does not exists": "Support existiert nicht", "Topic": "<PERSON>a", "Support": "Support", "Topics": "Themen", "Topic Management": "Themen-Verwaltung", "Duplicated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Support": "Support hinzufügen", "Support updated": "Support aktualisiert", "Topic created": "<PERSON>a er<PERSON>t", "All Tickets": "Alle Tickets", "Search result for: :name": "Suchergebnis für: :name", "My Tickets": "<PERSON><PERSON>", "Create a ticket": "Ein Ticket erstellen", "Ticket created": "Ticket erste<PERSON>t", "Reply created": "Antwort erstellt", "Can not add reply": "Kann keine Antwort hinzufügen", "All Topics": "Alle Themen", "New reply on ticket: #\" . $this->ticket->id))->view('Support::email.new_reply": "Neue Antwort auf Ticket: #\" . $this->ticket->id))->view('Support::email.new_reply", "Topic Tag": "Themen-Tag", "Closed": "Geschlossen", "Support Category": "Support-<PERSON><PERSON><PERSON>", "Add Topic": "<PERSON><PERSON>", "Edit topic: :name": "Thema bearbeiten: :name", "Add new topic": "Neues Thema hinzufügen", "All tickets": "Alle Tickets", "Search topic": "<PERSON>a suchen", "Agent": "Agent", "Unassigned": "<PERSON>cht zugewiesen", "View Ticket": "Ticket anzeigen", "All topics": "Alle Themen", "Topic Tags": "Themen-Tags", "Hello": "Hall<PERSON>", "You got new reply for ticket: #": "Sie haben eine neue Antwort für Ticket: #", "Reply content:": "Antwort-Inhalt:", "You can check the ticket here:": "<PERSON>e können das Ticket hier überprüfen:", "View ticket": "Ticket anzeigen", "View all": "Alle anzeigen", "Ticket Status": "Ticket-Status", "Save Status": "Status speichern", "User Notes": "Benutzer-Notizen", "Add note": "<PERSON><PERSON>", "Add user note": "Benutzer-<PERSON><PERSON>", "Old": "Alt", "New": "<PERSON>eu", "Please provide ticket content": "Bitte Ticket-Inhalt angeben", "Need Response": "Antwo<PERSON> er<PERSON>", "Support tickets": "Support-Tickets", "All categories": "Alle Kategorien", "How Can We Help?": "Wie können wir helfen?", "Find out more topics": "<PERSON><PERSON><PERSON> Themen finden", "Support Tickets": "Support-Tickets", "Search topic...": "Thema suchen...", "Popular Topics": "Beliebte Themen", "Create new ticket": "Neues Ticket erstellen", "Create ticket": "Ticket erstellen", "Ask a question": "Eine Frage stellen", "Ticket name": "Ticket-Name", "Last reply": "Letzte Antwort", "Showing :from - :to of :total tickets": "Zeige :from - :to von :total Tickets", "No ticket found": "<PERSON><PERSON>et gefunden", "Tags: ": "Tags: ", "Related topics": "Verwandte Themen", "Showing :from - :to of :total topics": "Zeige :from - :to von :total Themen", "No topic found": "<PERSON><PERSON> gefunden", "These credentials do not match our records.": "Diese Anmeldedaten stimmen nicht mit unseren Aufzeichnungen überein.", "Too many login attempts. Please try again in :seconds seconds.": "<PERSON>u viele Anmeldeversuche. Bitte versuchen Sie es in :seconds <PERSON><PERSON>nden erneut.", "Laravel Installer": "<PERSON><PERSON>-Installer", "Next Step": "Nächs<PERSON>", "Install": "Installieren", "The Following errors occurred:": "Die folgenden Fehler sind aufgetreten:", "Welcome": "<PERSON><PERSON><PERSON><PERSON>", "Easy Installation and Setup Wizard.": "Einfacher Installations- und Setup-Assistent.", "Check Requirements": "Anforderungen prüfen", "Step 1 | Server Requirements": "Schritt 1 | Server-<PERSON><PERSON>er<PERSON>", "Server Requirements": "Server-<PERSON><PERSON><PERSON><PERSON>", "Check Permissions": "Berechtigungen prüfen", "Step 2 | Permissions": "Schritt 2 | Berechtigungen", "Configure Environment": "Umgebung konfigurieren", "Step 3 | Environment Settings": "Schritt 3 | Umgebungseinstellungen", "Environment Settings": "Umgebungseinstellungen", "Please select how you want to configure the apps <code>.env</code> file.": "Bitte wählen Si<PERSON> au<PERSON>, wie Sie die <code>.env</code>-<PERSON><PERSON> der App konfigurieren möchten.", "Form Wizard Setup": "Formular-Assistent-Setup", "Classic Text Editor": "Klassischer Texteditor", "Step 3 | Environment Settings | Guided Wizard": "Schritt 3 | Umgebungseinstellungen | Geführter Assistent", "Guided <code>.env</code> Wizard": "Geführter <code>.env</code>-Assistent", "Environment": "Umgebung", "Database": "Datenbank", "Application": "<PERSON><PERSON><PERSON><PERSON>", "An environment name is required.": "Ein Umgebungsname ist erforderlich.", "App Name": "App-Name", "App Environment": "App-Umgebung", "Local": "<PERSON><PERSON>", "Development": "Entwicklung", "Qa": "QA", "Production": "Produktion", "Other": "<PERSON><PERSON>", "Enter your environment...": "Geben Sie Ihre Umgebung ein...", "App Debug": "App-Debug", "True": "<PERSON><PERSON><PERSON>", "False": "<PERSON><PERSON><PERSON>", "App Log Level": "App-Log-Level", "debug": "Debug", "info": "Info", "notice": "<PERSON><PERSON><PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "critical": "<PERSON><PERSON><PERSON>", "alert": "Alarm", "emergency": "Notfall", "App Url": "App-URL", "Admin Password": "Admin-Passwort", "Database Connection": "Datenbankverbindung", "mysql": "MySQL", "sqlite": "SQLite", "pgsql": "PostgreSQL", "sqlsrv": "SQL Server", "Database Host": "Datenbank-Host", "Database Port": "Datenbank-Port", "Database Name": "Datenbank-Name", "Database User Name": "Datenbank-Benutzername", "Database Password": "Datenbank-Passwort", "More Info": "Weitere Informationen", "Broadcasting, Caching, Session, &amp; Queue": "Broadcasting, Caching, Session &amp; Queue", "Cache Driver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Session Driver": "Session-<PERSON><PERSON><PERSON>", "Queue Driver": "Queue<PERSON><PERSON><PERSON><PERSON>", "Redis Driver": "Redis-Treiber", "Redis Host": "Redis-Host", "Redis Password": "Redis-Passwort", "Redis Port": "Redis-Port", "Mail": "E-Mail", "Mail Driver": "E-Mail-Treiber", "Mail Host": "E-Mail-Host", "Mail Port": "E-Mail-Port", "Mail Username": "E-Mail-Benutzername", "Mail Password": "E-Mail-Passwort", "Mail Encryption": "E-Mail-Verschlüsselung", "Pusher": "<PERSON><PERSON><PERSON>", "Pusher App Id": "<PERSON><PERSON>er-App-ID", "Pusher App Key": "Pusher-<PERSON>pp-Schl<PERSON><PERSON>", "Pusher App Secret": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Setup Database": "Datenbank einrichten", "Setup Application": "<PERSON><PERSON><PERSON><PERSON> ein<PERSON>", "Step 3 | Environment Settings | Classic Editor": "Schritt 3 | Umgebungseinstellungen | Klassischer Editor", "Classic Environment Editor": "Klassischer Umgebungseditor", "Save .env": ".env speichern", "Use Form Wizard": "Formular-<PERSON><PERSON><PERSON> ver<PERSON><PERSON>", "Save and Install": "Speichern und installieren", "Your .env file settings have been saved.": "Ihre .env-Datei-Einstellungen wurden gespeichert.", "Unable to save the .env file, Please create it manually.": "Die .env-<PERSON><PERSON> konnte nicht gespeichert werden. Bitte erstellen Si<PERSON> sie manuell.", "Laravel Installer successfully INSTALLED on ": "Laravel-Installer erfolgreich INSTALLIERT am ", "Installation Finished": "Installation abgeschlossen", "Application has been successfully installed.": "Die Anwendung wurde erfolgreich installiert.", "Migration &amp; Seed Console Output:": "Migration &amp; Seed <PERSON>-Ausgabe:", "Application Console Output:": "Anwendungs-Konsolen-Ausgabe:", "Installation Log Entry:": "Installations-Log-Eintrag:", "Final .env File:": "Finale .env-Datei:", "Click here to exit": "Hier klicken zum Beenden", "Laravel Updater": "<PERSON><PERSON>-Updater", "Welcome To The Updater": "<PERSON><PERSON><PERSON><PERSON> beim <PERSON>", "Welcome to the update wizard.": "<PERSON><PERSON><PERSON><PERSON> beim <PERSON>-Assistenten.", "Overview": "Übersicht", "There is 1 update.|There are :number updates.": "Es gibt 1 Update.|Es gibt :number Updates.", "Finished": "Abgeschlossen", "Application\\'s database has been successfully updated.": "Die Anwendungsdatenbank wurde erfolgreich aktualisiert.", "Laravel Installer successfully UPDATED on ": "Laravel-Installer erfolgreich AKTUALISIERT am ", "&laquo; Previous": "&laquo; Vorherige", "Next &raquo;": "Nächste &raquo;", "Passwords must be at least eight characters and match the confirmation.": "Passwörter müssen mindestens acht Zeichen lang sein und mit der Bestätigung übereinstimmen.", "Your password has been reset!": "Ihr Passwort wurde zurückgesetzt!", "This password reset token is invalid.": "Dieser Passwort-Reset-Token ist ungültig.", "Confirm password": "Passwort bestätigen", "This is a secure area of the application. Please confirm your password before continuing.": "Dies ist ein sicherer Bereich der Anwendung. Bitte bestätigen Sie Ihr Passwort, bevor <PERSON> fortfahren.", "Please confirm access to your account by entering one of your emergency recovery codes.": "Bitte bestätigen Sie den Zugang zu Ihrem Konto, indem Sie einen Ihrer Notfall-Wiederherstellungscodes eingeben.", "Recovery Code": "Wiederherstellungscode", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "Bitte bestätigen Sie den Zugang zu Ihrem Konto, indem Si<PERSON> den von Ihrer Authentifizierungs-App bereitgestellten Code eingeben.", "Use an authentication code": "Authentifizierungscode verwenden", "Use a recovery code": "Wiederherstellungscode verwenden", "Unauthorized": "Nicht autorisiert", "Forbidden": "Verboten", "Page not found": "Seite nicht gefunden", "Sorry, we couldn't find the page you're looking for.": "Entschuldigung, wir konnten die gesuchte Seite nicht finden.", "Page Expired": "Seite abgelaufen", "Too Many Requests": "Zu viele Anfragen", "Server Error": "Server<PERSON><PERSON><PERSON>", "Service Unavailable": "Service nicht verfügbar", "Oh no": "Oh nein", "Go Home": "Zur Startsei<PERSON>", "installer_messages.environment.classic.templateTitle": "Schritt 3 | Umgebungseinstellungen | Klassischer Editor", "installer_messages.environment.classic.title": "Klassischer Umgebungseditor", "installer_messages.environment.classic.save": ".env speichern", "installer_messages.environment.classic.back": "Formular-<PERSON><PERSON><PERSON> ver<PERSON><PERSON>", "installer_messages.environment.classic.install": "Speichern und installieren", "installer_messages.environment.wizard.templateTitle": "Schritt 3 | Umgebungseinstellungen | Geführter Assistent", "installer_messages.environment.wizard.title": "Geführter .env-Assistent", "Site Name": "Site-Name", "installer_messages.environment.wizard.form.app_name_placeholder": "App-Name", "installer_messages.environment.wizard.form.app_environment_label": "App-Umgebung", "installer_messages.environment.wizard.form.app_environment_label_local": "<PERSON><PERSON>", "installer_messages.environment.wizard.form.app_environment_label_developement": "Entwicklung", "installer_messages.environment.wizard.form.app_environment_label_qa": "QA", "installer_messages.environment.wizard.form.app_environment_label_production": "Produktion", "installer_messages.environment.wizard.form.app_environment_label_other": "<PERSON><PERSON>", "installer_messages.environment.wizard.form.app_environment_placeholder_other": "Geben Sie Ihre Umgebung ein...", "installer_messages.environment.wizard.form.app_debug_label": "App-Debug", "installer_messages.environment.wizard.form.app_debug_label_true": "<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_debug_label_false": "<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_log_level_label": "App-Log-Level", "installer_messages.environment.wizard.form.app_log_level_label_debug": "Debug", "installer_messages.environment.wizard.form.app_log_level_label_info": "Info", "installer_messages.environment.wizard.form.app_log_level_label_notice": "<PERSON><PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_log_level_label_warning": "<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_log_level_label_error": "<PERSON><PERSON>", "installer_messages.environment.wizard.form.app_log_level_label_critical": "<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_log_level_label_alert": "Alarm", "installer_messages.environment.wizard.form.app_log_level_label_emergency": "Notfall", "Site url": "Site-URL", "installer_messages.environment.wizard.form.app_url_placeholder": "App-URL", "installer_messages.environment.wizard.form.buttons.setup_database": "Datenbank einrichten", "installer_messages.environment.wizard.form.db_connection_label": "Datenbankverbindung", "installer_messages.environment.wizard.form.db_connection_label_mysql": "MySQL", "installer_messages.environment.wizard.form.db_connection_label_sqlite": "SQLite", "installer_messages.environment.wizard.form.db_connection_label_pgsql": "PostgreSQL", "installer_messages.environment.wizard.form.db_connection_label_sqlsrv": "SQL Server", "installer_messages.environment.wizard.form.db_host_label": "Datenbank-Host", "installer_messages.environment.wizard.form.db_host_placeholder": "Datenbank-Host", "installer_messages.environment.wizard.form.db_port_label": "Datenbank-Port", "installer_messages.environment.wizard.form.db_port_placeholder": "Datenbank-Port", "installer_messages.environment.wizard.form.db_name_label": "Datenbank-Name", "installer_messages.environment.wizard.form.db_name_placeholder": "Datenbank-Name", "installer_messages.environment.wizard.form.db_username_label": "Datenbank-Benutzername", "installer_messages.environment.wizard.form.db_username_placeholder": "Datenbank-Benutzername", "installer_messages.environment.wizard.form.db_password_label": "Datenbank-Passwort", "installer_messages.environment.wizard.form.db_password_placeholder": "Datenbank-Passwort", "installer_messages.environment.wizard.form.app_admin_email_placeholder": "Admin-E-Mail", "installer_messages.environment.wizard.form.app_admin_password_placeholder": "Admin-Passwort", "Test DB": "DB testen", "installer_messages.environment.wizard.form.buttons.install": "<PERSON><PERSON><PERSON><PERSON> ein<PERSON>", "installer_messages.environment.wizard.form.app_tabs.broadcasting_title": "Broadcasting, Caching, Session &amp; Queue", "installer_messages.environment.wizard.form.app_tabs.broadcasting_label": "Broadcast-<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.more_info": "Weitere Informationen", "installer_messages.environment.wizard.form.app_tabs.broadcasting_placeholder": "Broadcast-<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.cache_label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.cache_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.session_label": "Session-<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.session_placeholder": "Session-<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.queue_label": "Queue<PERSON><PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.queue_placeholder": "Queue<PERSON><PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.redis_label": "Redis-Treiber", "installer_messages.environment.wizard.form.app_tabs.redis_host": "Redis-Host", "installer_messages.environment.wizard.form.app_tabs.redis_password": "Redis-Passwort", "installer_messages.environment.wizard.form.app_tabs.redis_port": "Redis-Port", "installer_messages.environment.wizard.form.app_tabs.mail_label": "E-Mail", "installer_messages.environment.wizard.form.app_tabs.mail_driver_label": "E-Mail-Treiber", "installer_messages.environment.wizard.form.app_tabs.mail_driver_placeholder": "E-Mail-Treiber", "installer_messages.environment.wizard.form.app_tabs.mail_host_label": "E-Mail-Host", "installer_messages.environment.wizard.form.app_tabs.mail_host_placeholder": "E-Mail-Host", "installer_messages.environment.wizard.form.app_tabs.mail_port_label": "E-Mail-Port", "installer_messages.environment.wizard.form.app_tabs.mail_port_placeholder": "E-Mail-Port", "installer_messages.environment.wizard.form.app_tabs.mail_username_label": "E-Mail-Benutzername", "installer_messages.environment.wizard.form.app_tabs.mail_username_placeholder": "E-Mail-Benutzername", "installer_messages.environment.wizard.form.app_tabs.mail_password_label": "E-Mail-Passwort", "installer_messages.environment.wizard.form.app_tabs.mail_password_placeholder": "E-Mail-Passwort", "installer_messages.environment.wizard.form.app_tabs.mail_encryption_label": "E-Mail-Verschlüsselung", "installer_messages.environment.wizard.form.app_tabs.mail_encryption_placeholder": "E-Mail-Verschlüsselung", "installer_messages.environment.wizard.form.app_tabs.pusher_label": "<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.pusher_app_id_label": "<PERSON><PERSON>er-App-ID", "installer_messages.environment.wizard.form.app_tabs.pusher_app_id_palceholder": "<PERSON><PERSON>er-App-ID", "installer_messages.environment.wizard.form.app_tabs.pusher_app_key_label": "Pusher-<PERSON>pp-Schl<PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.pusher_app_key_palceholder": "Pusher-<PERSON>pp-Schl<PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_label": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_palceholder": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "installer_messages.environment.menu.templateTitle": "Schritt 3 | Umgebungseinstellungen", "installer_messages.environment.menu.title": "Umgebungseinstellungen", "installer_messages.environment.menu.desc": "Bitte wählen Si<PERSON> au<PERSON>, wie Sie die .env-Datei der App konfigurieren möchten.", "installer_messages.environment.menu.wizard-button": "Formular-Assistent-Setup", "installer_messages.environment.menu.classic-button": "Klassischer Texteditor", "installer_messages.final.templateTitle": "Installation abgeschlossen", "installer_messages.final.title": "Die Anwendung wurde erfolgreich installiert.", "installer_messages.final.log": "Installations-Log-Eintrag:", "installer_messages.final.exit": "Hier klicken zum Beenden", "installer_messages.updater.title": "<PERSON><PERSON>-Updater", "installer_messages.title": "<PERSON><PERSON>-Installer", "installer_messages.forms.errorTitle": "Die folgenden Fehler sind aufgetreten:", "installer_messages.permissions.templateTitle": "Schritt 2 | Berechtigungen", "installer_messages.permissions.title": "Berechtigungen", "installer_messages.permissions.next": "Umgebung konfigurieren", "installer_messages.requirements.templateTitle": "Schritt 1 | Server-<PERSON><PERSON>er<PERSON>", "installer_messages.requirements.title": "Server-<PERSON><PERSON><PERSON><PERSON>", "installer_messages.requirements.next": "Berechtigungen prüfen", "installer_messages.updater.final.title": "Abgeschlossen", "installer_messages.updater.final.exit": "Hier klicken zum Beenden", "installer_messages.updater.welcome.title": "<PERSON><PERSON><PERSON><PERSON> beim <PERSON>", "installer_messages.updater.overview.message": "Es gibt 1 Update.|Es gibt :number Updates.", "installer_messages.updater.overview.install_updates": "Updates installieren", "installer_messages.updater.welcome.message": "<PERSON><PERSON><PERSON><PERSON> beim <PERSON>-Assistenten.", "installer_messages.next": "Nächs<PERSON>", "installer_messages.welcome.templateTitle": "<PERSON><PERSON><PERSON><PERSON>", "Booking Core :version Installer": "Booking Core :version Installer", "installer_messages.welcome.message": "Einfacher Installations- und Setup-Assistent.", "installer_messages.welcome.next": "Anforderungen prüfen", "Log file >50M, please download it.": "Log-Datei >50M, bitte herunterladen.", "Context": "Kontext", "Line number": "Zeilennummer", "pagination.previous": "« Vorherige", "pagination.next": "Nächste »", "Booking ID": "Buchungs-ID", "Booking Detail": "Buchungsdetails", "Customer Information": "Kunden-Informationen", "Your Booking": "<PERSON><PERSON><PERSON>", "Start date:": "Startdatum:", "Total:": "Gesamt:", "Paid:": "Bezahlt:", "Remain:": "Verbleibt:", "End date": "Enddatum", "Durations": "<PERSON><PERSON>", "Details": "Details", "1. Content": "1. <PERSON><PERSON>", "2. Locations": "2. <PERSON><PERSON><PERSON>", "3. Pricing": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4. Attributes": "4. Attribute", "No Boat": "<PERSON><PERSON>", "Last Updated": "Zuletzt aktualisiert", "\"Do you want to recovery?\"": "\"<PERSON><PERSON><PERSON><PERSON> Si<PERSON> wiederherstellen?\"", "\"Do you want to permanently delete?\"": "\"<PERSON><PERSON>cht<PERSON> Si<PERSON> dauerhaft löschen?\"", "Del": "Löschen", "\"Do you want to delete?\"": "\"<PERSON><PERSON>chten Sie löschen?\"", "Make hide": "Ausblenden", "Make publish": "Veröffentlichen", "Boat by :name": "<PERSON><PERSON> von :name", "View all (:total)": "Alle anzeigen (:total)", "You got reply from vendor. ": "Sie haben eine Antwort vom Anbieter erhalten. ", "Service:": "Service:", "Your note:": "<PERSON><PERSON><PERSON> Notiz:", "Here is the message from vendor:": "Hier ist die Nachricht vom Anbieter:", "New booking has been made": "Neue Buchung wurde erstellt", "Your service has new booking": "Ihr Service hat eine neue Buchung", "Thank you for booking with us. Here are your booking information:": "Vielen Dank für Ihre Buchung bei uns. Hier sind Ihre Buchungsinformationen:", "Customer information": "Kunden-Informationen", "Tickets / Guests Information:": "Tickets / Gäste-Informationen:", "Guest #:number": "Gast #:number", "First Name: ": "Vorname: ", "Last Name: ": "Nachname: ", "Email: ": "E-Mail: ", "Phone: ": "Telefon: ", "The booking status has been updated": "Der Buchungsstatus wurde aktualisiert", "Personal Information": "Persönliche Informationen", "Guests Information": "Gäste-Informationen", "Number:": "Nummer:", "5. Ical": "5. iCal", "No Car": "<PERSON>in <PERSON>", "Car by :name": "Auto von :name", "100$": "100€", "Bonus 10%": "Bonus 10%", "Bonus 15%": "Bonus 15%", "only for Services": "nur für Services", "Manage Coupon": "Gutschein verwalten", "Add Coupon": "Gutschein hinzufügen", "Showing :from - :to of :total coupon": "Zeige :from - :to von :total Gutscheinen", "No Coupon": "<PERSON><PERSON>", "Print Ticket": "Ticket drucken", "Event by :name": "Event von :name", ":from to :to": ":from bis :to", ":duration hrs": ":duration Std", "Arrival Time ": "Ankunftszeit ", "No Flight": "<PERSON><PERSON>", "Do you want to permanently delete?": "Möchten Si<PERSON> dauerhaft löschen?", "Add new seat": "Neuen Sitzplatz hinzufügen", "Manage Seats": "Sitzplätze verwalten", "1. Seat Content": "1. Sitzplatz-Inhalt", "Back to flight": "Zurück zum Flug", "Add Seat": "Sitzplatz hinzufügen", "Flight id": "Flug-ID", "Flight by :name": "Flug von :name", "Check in:": "Check-in:", "Adults:": "Erwachsene:", "Children:": "Kinder:", "Check out": "Check-out", "Hotel by :name": "Hotel von :name", "Recovery news": "News wiederherstellen", "Manage news": "News verwalten", "No Space": "<PERSON><PERSON>", "Space by :name": "<PERSON><PERSON> von :name", "There is no layer yet!": "Es gibt noch keine <PERSON>bene!", "Click button bellow to start adding layer": "<PERSON>licken Si<PERSON> auf die Schaltfläche unten, um eine Ebene hinzuzufügen", "4. Availability": "4. Verfügbarkeit", "5. Attributes": "5. Attribute", "6. Ical": "6. iCal", "No Tours": "<PERSON><PERSON>", "Tour by :name": "Tour von :name", "Setup Two Factor Authentication": "Zwei-Faktor-Authentifizierung einrichten", "You have enabled factor authentication": "Sie haben die Faktor-Authentifizierung aktiviert", "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.": "Wenn die Zwei-Faktor-Authentifizierung aktiviert ist, werden Sie während der Authentifizierung zur Eingabe eines sicheren, zufälligen Tokens aufgefordert. Sie können diesen Token von der Google Authenticator-App Ihres Telefons abrufen.", "Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.": "Die Zwei-Faktor-Authentifizierung ist jetzt aktiviert. Scannen Sie den folgenden QR-Code mit der Authenticator-App Ihres Telefons.", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "Speichern Sie diese Wiederherstellungscodes in einem sicheren Passwort-Manager. <PERSON><PERSON> können verwendet werden, um den Zugang zu Ihrem Konto wiederherzustellen, wenn Ihr Zwei-Faktor-Authentifizierungsgerät verloren geht.", "Disable two factor authentication": "Zwei-Faktor-Authentifizierung deaktivieren", "You have not enabled factor authentication": "Sie haben die Faktor-Authentifizierung nicht aktiviert", "Enable now": "Jetzt aktivieren", "Two-factor authentication adds an additional layer of security to your account by requiring more than just a password to sign in": "Die Zwei-Faktor-Authentifizierung fügt Ihrem Konto eine zusätzliche Sicherheitsebene hinzu, indem sie mehr als nur ein Passwort für die Anmeldung erfordert", "INVOICE": "RECHNUNG", "Invoice #: :number": "Rechnung #: :number", "Created: :date": "Erstellt: :date", "Amount due:": "Fälliger Betrag:", "Billing to:": "Rechnung an:", "Current Password": "Aktuelles Passwort", "New Password": "Neues Passwort", "* Require at least one uppercase, one lowercase letter, one number and one symbol.": "* Erfordert mindestens einen Großbuchstaben, einen Kleinbuchstaben, eine Zahl und ein Symbol.", "New Password Again": "Neues Passwort wiederholen", "My Profile": "<PERSON><PERSON>", "Become a vendor": "Anbieter werden", "Log Out": "Abmelden", "Back to Homepage": "Zurück zur Startseite", "Choose your pricing plan": "Wählen Sie Ihren Preisplan", "Save up to 10%": "Sparen Sie bis zu 10%", "Monthly": "<PERSON><PERSON><PERSON>", "Annual": "<PERSON><PERSON><PERSON><PERSON>", "Recommended": "<PERSON><PERSON><PERSON><PERSON>", "Current Plan": "Aktueller Plan", "Repurchase": "<PERSON><PERSON><PERSON> kaufen", "Select": "Auswählen", "My Current Plan": "Mein aktueller Plan", "No Items": "<PERSON><PERSON>", "Hi, I'm :name": "Hallo, ich bin :name", "View all reviews (:total)": "Alle Bewertungen anzeigen (:total)", ":count review": ":count <PERSON><PERSON><PERSON><PERSON>", ":count reviews": ":count <PERSON><PERSON><PERSON><PERSON>", "About Yourself": "Über Sie", "Browse": "Durchsuchen", "Error upload...": "Upload-Fehler...", "No Image": "<PERSON><PERSON>", "Location Information": "Standort-Informationen", "Address2": "Adresse2", "Delete account": "Konto löschen", "Your account will be permanently deleted. Once you delete your account, there is no going back. Please be certain.": "Ihr Konto wird dauerhaft gelöscht. Sobald Sie Ihr Konto löschen, gibt es kein Zurück. Bitte seien Si<PERSON> sicher.", "Delete your account": "Ihr Konto löschen", "Confirm permanently delete account": "Dauerhaftes Löschen des Kontos bestätigen", "Select File": "<PERSON>i ausw<PERSON>hlen", "N/A": "N/V", "Select Files": "<PERSON><PERSON> au<PERSON>wählen", "Verify Phone": "Telefon verifizieren", "Verification data": "Verifizierungsdaten", "Update verification data": "Verifizierungsdaten aktualisieren", "Verify": "Verifizieren", "Buy": "<PERSON><PERSON><PERSON>", "Sorry, no options found": "Entschuldigung, keine Optionen gefunden", "How much would you like to deposit?": "Wie viel möchten Si<PERSON> e<PERSON>en?", "Deposit amount": "Einzahlungsbetrag", "Process now": "Jetzt verarbeiten", ":amount": ":amount", "Latest Transactions": "Neueste Transaktionen", "Gateway": "Gateway", "Deposit by :name": "Einzahlung von :name", "WishList": "Wunschliste", "Showing :from - :to of :total": "Zeige :from - :to von :total", ":number Reviews": ":number Bewertungen", ":number Review": ":number Bewertung", "Remove": "Entfernen", "Advanced Filter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Customer name": "Kundenname", "Customer Name": "Kundenname", "From - To": "<PERSON> <PERSON> <PERSON><PERSON>", "We couldn't find any boats.": "Wir konnten keine Boote finden.", "Try changing your filter criteria": "Versuchen Sie, <PERSON><PERSON><PERSON> Filterkriterien zu ändern", "Please select start date": "Bitte Startdatum auswählen", "Please select at least one number": "Bitte mindestens eine Nummer auswählen", "Name is Required": "Name ist erforderlich", "Email is Required": "E-Mail ist erforderlich", "Boat Video": "Boot-Video", "from :number reviews": "von :number Bewertungen", ":number% of guests recommend": ":number% der Gäste empfehlen", "Length Boat": "Boot-Länge", "from": "von", "/per hour": "/pro Stunde", "/per day": "/pro Tag", "Book Now": "Jetzt buchen", "Contact Now": "Jetzt kontaktieren", "Book": "Buchen", "Return on same-day": "Rückgabe am selben Tag", "Return on another day": "Rückgabe an einem anderen Tag", "Days": "Tage", "Select Dates": "<PERSON><PERSON><PERSON>", "Book :number days in advance": ":number Tage im Voraus buchen", "Book :number day in advance": ":number Tag im Voraus buchen", "Extra prices:": "Zusatzpreise:", "BOOK NOW": "JETZT BUCHEN", "Included": "Inbegriffen", "Excluded": "Ausgeschlossen", "Included/Excluded": "Inbegriffen/Ausgeschlossen", "You might also like": "Das könnte Ihnen auch gefallen", "Specs & Details": "Spezifikationen & Details", "All :name": "Alle :name", "Where are you going?": "Wohin gehen <PERSON>?", "Search for...": "Suchen nach...", "FILTER BY": "FILTERN NACH", "APPLY": "ANWENDEN", "Sort by:": "Sortieren nach:", "Price (Low to high)": "<PERSON><PERSON> (<PERSON><PERSON><PERSON> zu hoch)", "Price (High to low)": "Preis (<PERSON><PERSON> zu niedrig)", "Rating (High to low)": "Bewertung (<PERSON><PERSON> zu niedrig)", "Apply Filters": "<PERSON><PERSON> anwenden", "Price filter": "Preisfilter", "More filters": "<PERSON><PERSON><PERSON>", "Availability Boats": "Boot-Verfügbarkeit", "We couldn't find any cars.": "Wir konnten keine Autos finden.", "Clear Filters": "<PERSON><PERSON>", "Please select Start and End date": "Bitte Start- und Enddatum auswählen", "Car Video": "Auto-Video", "Stay at least :number days": "Mindestens :number <PERSON><PERSON> bleiben", "Stay at least :number day": "Mindestens :number Tag bleiben", "/day": "/Tag", "Availability Cars": "Auto-Verfügbarkeit", "Header Settings": "Header-Einstellungen", "Enable Header Sticky": "Sticky Header aktivieren", "We couldn't find any events.": "Wir konnten keine Events finden.", "Event Video": "Event-Video", "People interest: :number": "Interessierte Personen: :number", "per ticket": "pro Ticket", "Start Time: :time": "Startzeit: :time", "Availability Events": "Event-Verfügbarkeit", "No Event": "Kein Event", "Flight not found": "Flug nicht gefunden", "Please select at least one guest": "Bitte mindestens einen Gast auswählen", "View on map": "<PERSON><PERSON> anzeigen", "People": "<PERSON>en", "bathrooms": "<PERSON><PERSON><PERSON>", "beds": "<PERSON><PERSON>", "Ages 12+": "Alter 12+", "Ages 2–12": "<PERSON><PERSON> 2–12", ":count Guest in maximum": ":count <PERSON><PERSON> maximal", ":count Guests in maximum": ":count <PERSON><PERSON><PERSON> maximal", "You might also like...": "Das könnte Ihnen auch gefallen...", "Video": "Video", "1 Adult": "1 Erwachsener", ":count Adults": ":count <PERSON><PERSON><PERSON><PERSON><PERSON>", ":count Child": ":count Kind", ":count Children": ":count Kinder", "City or airport": "Stadt oder Flughafen", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                        </span>\r\n\t\t\t\t\t@endforeach\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dropdown-menu select-seat-type-dropdown\" >\r\n\t\t\t@foreach($seatType as $type)\r\n\t\t\t\t<?php\r\n\t\t\t\t$inputName = 'seat_type_'.$type->code;\r\n\t\t\t\t$inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n\t\t\t\t;?>\r\n\r\n\t\t\t\t<div class=\"dropdown-item-row\">\r\n\t\t\t\t\t<div class=\"label\">{{__('Adults :type": ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                        </span>\r\n\t\t\t\t\t@endforeach\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dropdown-menu select-seat-type-dropdown\" >\r\n\t\t\t@foreach($seatType as $type)\r\n\t\t\t\t<?php\r\n\t\t\t\t$inputName = 'seat_type_'.$type->code;\r\n\t\t\t\t$inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n\t\t\t\t;?>\r\n\r\n\t\t\t\t<div class=\"dropdown-item-row\">\r\n\t\t\t\t\t<div class=\"label\">{{__('Erwachsene :type", "avg/person": "Durchschnitt/Person", "Take off": "Abflug", "Landing": "Landung", "Choose": "Auswählen", "Check-in": "Check-in", "Pay Amount": "Zahlungsbetrag", "We couldn't find any spaces.": "Wir konnten keine Räume finden.", "Availability Spaces": "Raum-Verfügbarkeit", "We couldn't find any hotels.": "Wir konnten keine Hotels finden.", "Hotel Video": "Hotel-Video", "Rules": "Regeln", "Check In": "Check-in", "Check Out": "Check-out", "Hotel Policies": "Hotel-Rich<PERSON><PERSON><PERSON>", "Show All": "Alle anzeigen", "Related Hotel": "Verwandte Hotels", "/night": "/Nacht", "Available Rooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Check In - Out": "Check-in - Check-out", "Check Availability": "Verfügbarkeit prüfen", "Room Footage": "Zimmergröße", "No. Beds": "<PERSON><PERSON><PERSON>", "No. Adults": "<PERSON><PERSON><PERSON>", "No. Children": "<PERSON><PERSON><PERSON>", "Total Room": "<PERSON><PERSON> gesamt", "Total Price": "Gesamtpreis", "No room available with your selected date. Please change your search critical": "<PERSON><PERSON> für Ihr ausgewähltes Datum verfügbar. Bitte ändern Sie Ihre Suchkriterien", "What's Nearby": "Was ist in der Nähe", "Availability Rooms": "Zimmer-Verfügbarkeit", "No Hotel": "Kein Hotel", "Add new room": "Neues Zimmer hinzufügen", "1. Room Content": "1. <PERSON><PERSON><PERSON>", "2. Pricing": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3. Attributes": "3. Attribute", "4. Ical": "4. iCal", "Showing :from - :to of :total Rooms": "Zeige :from - :to von :total Zimmern", "No Room": "<PERSON><PERSON>", "Upgrade to PRO to unlock unlimited access to all of our features": "Auf PRO upgraden für unbegrenzten Zugang zu allen Funktionen", "Explore the place": "Den Ort erkunden", "The City Maps": "Die Stadtkarten", "FEATURED ARTICLE": "HERVORGEHOBENER ARTIKEL", "Read More": "<PERSON><PERSON> lesen", "Showing :from - :to of :total posts": "Zeige :from - :to von :total Beiträgen", "Sorry, but nothing matched your search terms. Please try again with some different keywords.": "Entschuldigung, aber nichts entsprach Ihren Suchbegriffen. Bitte versuchen Sie es mit anderen Stichwörtern erneut.", "BY ": "VON ", "DATE ": "DATUM ", "Tags:": "Tags:", "Search ...": "Suchen ...", "Oops! It looks like you're lost.": "Ups! Es sieht so aus, als wären Si<PERSON> verloren.", "The page you're looking for isn't available. Try to search again or use the go to.": "Die gesuchte Seite ist nicht verfügbar. Versuchen Si<PERSON> erneut zu suchen oder verwenden Sie den Link.", "Go back to homepage": "Zurück zur Startseite", "Space Video": "Raum-Video", "No. People": "<PERSON><PERSON><PERSON>", "We couldn't find any tours.": "Wir konnten keine Touren finden.", "Book now": "Jetzt buchen", "Please select Start date": "Bitte Startdatum auswählen", "Tour Video": "Tour-Video", "Group Size": "Gruppengröße", ":number persons": ":number Personen", ":number person": ":number Person", "Tour Location": "Tour-Standort", "Tour Start Date": "Tour-Startdatum", "Tour End Date": "Tour-Enddatum", "per person": "pro Person", "Message host": "Gastgeber kontaktieren", "Availability Tours": "Tour-Verfügbarkeit", "Showing :from - :to of :total tours": "Zeige :from - :to von :total Touren", "Enable Preload": "Preload aktivieren", "Logo Dark": "Logo dunkel", "Page 404 settings": "Seite 404 Einstellungen", "Settings for 404 error page": "Einstellungen für 404-Fehlerseite", "Error 404 banner": "<PERSON><PERSON> 404 Banner", "Error title": "<PERSON>hler-Tite<PERSON>", "Error desc": "Fehler-Beschreibung", "FEATURE": "FEATURE", ":count enrolled on this course": ":count in diesem Kurs eingeschrieben", "Last updated :date_update": "Zuletzt aktualisiert :date_update", "Add To Cart": "In den Warenkorb", "Buy Now": "Jetzt kaufen", "Lessons": "Lektionen", "Quizzes": "Quiz", "Skill level": "Fähigkeitslevel", "Instructor": "Dozent", "Instructor Rating": "Dozenten-Bewertung", "You May Like": "Das könnte Ihnen gefallen", "10,000+ unique online course list designs": "10.000+ einzigartige Online-Kurs-Listen-Designs", "Expand All Sections": "Alle Abschnitte erweitern", "Show more": "<PERSON><PERSON> anzeigen", "All Levels": "Alle Level", "5": "5", "4.0 & up": "4,0 & h<PERSON><PERSON>", "3.0 & up": "3,0 & h<PERSON><PERSON>", "2.0 & up": "2,0 & h<PERSON><PERSON>", "1.0 & up": "1,0 & h<PERSON><PERSON>", "FEATURED": "HERVORGEHOBEN", "lesson": "Lektion", "lessons": "Lektionen", "Showing": "<PERSON><PERSON><PERSON>", "total results": "Ergebnisse gesamt", "Newest": "Neueste", "Oldest": "Älteste", "Price [high to low]": "Preis [hoch zu niedrig]", "Price [low to high]": "<PERSON><PERSON> [ni<PERSON><PERSON> zu hoch]", "Rating [high to low]": "Bewertung [hoch zu niedrig]", "Explore": "Erkunden", "Log in": "Anmelden", "Call us": "<PERSON><PERSON><PERSON> uns an", "Sign up": "Registrieren", "My Courses": "<PERSON><PERSON>", "Popular Right Now": "Gerade beliebt", "PRESS ENTER TO SEE ALL SEARCH RESULTS": "ENTER DRÜCKEN, UM ALLE SUCHERGEBNISSE ZU SEHEN", "Banner Sub Title": "Banner-Untertitel", "Enable review system for News?": "Bewertungssystem für News aktivieren?", "Turn on the mode for reviewing news": "Modus für News-Bewertung aktivieren", "Fb": "Fb", "Tw": "Tw", "Linkedin": "LinkedIn", "Ln": "Ln", "Pinterest": "Pinterest", "Pin": "<PERSON>n", "Prev": "<PERSON><PERSON><PERSON><PERSON>", "Related Posts": "Verwandte Beiträge", "What are you looking for?": "Wonach suchen <PERSON>?", "All Categories": "Alle Kategorien", "Don't have an account yet?": "Haben <PERSON> noch kein Konto?", "Sign up for free": "Kostenlos registrieren", "Already have an account?": "Haben <PERSON> bereits ein Konto?", "Go Back To Homepage": "Zurück zur Startseite", "Leave A Review": "Eine Bewertung hinterlassen", "Enter Title": "Titel e<PERSON>ben", "Rating": "Bewertung", "Five Star": "<PERSON><PERSON><PERSON><PERSON>", "Four Star": "Vier Sterne", "Three Star": "<PERSON><PERSON>", "Two Star": "Zwei Sterne", "One Star": "Ein Stern", "Submit Review": "Bewertung absenden", "reviews": "Bewertungen", "review": "Bewertung", ":from - :to of :total+ :review available": ":from - :to von :total+ :review verfügbar", "Agent Single": "Einzelner Agent", " Listings": " <PERSON><PERSON><PERSON>", "Mobile: ": "Mobil: ", "View My Listing": "<PERSON><PERSON> anzeigen", "Listing": "<PERSON><PERSON><PERSON>", "Your Name": "Ihr Name", "Your Message": "<PERSON><PERSON><PERSON>", "Search results": "Suchergebnisse", "Sort by": "Sortieren nach", "Name ( a -> z )": "Name ( a -> z )", "Name ( z -> a )": "Name ( z -> a )", "Listings": "<PERSON><PERSON><PERSON>", "Advanced Search": "Erweiterte Suche", "Hide Filter": "<PERSON><PERSON> au<PERSON>", "All Agents": "Alle Agenten", "Find Agent": "Agent finden", "Enter Agent Name": "Agent-<PERSON> e<PERSON><PERSON>", "Agency": "Agentur", "(:rate_agv out of 5)": "(:rate_agv von 5)", "Write a Review": "Eine Bewertung schreiben", "Your Rating & Review": "Ihre Bewertung & Rezension", "Min Area": "<PERSON><PERSON>", "Max Area": "<PERSON><PERSON>", "Advanced features": "Erweiterte Funktionen", "Show Filter": "Filter anzeigen", "Agents": "<PERSON><PERSON>", "Search Agent": "Agent suchen", "Create Agent": "Agent <PERSON><PERSON><PERSON>", "Search Agency": "Agentur suchen", "Send Message": "Nachricht senden", "Become a Real Estate Agent": "Immobilienmakler werden", "We only work with the best companies around the globe": "Wir arbeiten nur mit den besten Unternehmen weltweit", "Register Now": "Jetzt registrieren", "Contact Information": "Kontaktinformationen", "Logo Transparent": "Logo transparent", "Logo Mobile": "Logo mobil", "Contact Title": "Kontakt-Titel", "Contact Sub Title": "Kontakt-Untertitel", "Contact Banner": "Kontakt-Banner", "Enable reCapcha Form": "reCAPTCHA-Formular aktivieren", "Turn on the mode for contact form": "Modus für Kontaktformular aktivieren", "Contact Locations": "Kontakt-Standorte", "Contact partners": "Kontakt-Partner", "Contact partner title": "Kontakt-Partner-Titel", "Contact partner sub title": "Kontakt-Partner-Untertitel", "Contact partner button text": "Kontakt-Partner-Button-Text", "Contact partner button link": "Kontakt-Partner-Button-Link", "Views": "Auf<PERSON><PERSON>", "Total Views": "Aufrufe gesamt", "Total Visitor Reviews": "Besucher-Bewertungen gesamt", "Favorites": "<PERSON><PERSON>", "Total Favorites": "<PERSON>n gesamt", "Recent Activities": "Aktuelle Aktivitäten", "Login with Facebook": "Mit Facebook anmelden", "Login with Google": "Mit Google anmelden", "Login with Twitter": "Mit Twitter anmelden", "Lost your password?": "Passwort vergessen?", "I have read and accept the Terms and Privacy Policy?": "Ich habe die AGB und Datenschutzrichtlinie gelesen und akzeptiert?", "Already have an account? ": "Haben <PERSON> bereits ein Konto? ", "Agent Dashboard": "Agent-Dash<PERSON>", "Create Listing": "Ang<PERSON><PERSON> erstellen", "Dashboard Navigation": "Dashboard-Navigation", "© 2020 Find House. Made with love.": "© 2020 Find House. Mit Liebe gemacht.", "View All": "Alle anzeigen", "views": "Auf<PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": "Immobilien", "/mo": "/Monat", "Beds:": "Betten:", "Baths:": "Bäder:", "Sq Ft:": "Quadratfuß:", "Search Here": "<PERSON><PERSON> <PERSON>en", "Scroll Down": "Nach unten scrollen", "to discover more": "um mehr zu entdecken", "Beds": "<PERSON><PERSON>", "Baths": "<PERSON><PERSON><PERSON>", "Sq Ft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Show More": "<PERSON><PERSON> anzeigen", "Property Details": "Immobilien-Details", "Property ID": "Immobilien-ID", "Property Size": "Immobilien-Größe", "None": "<PERSON><PERSON>", "Garage": "Garage", "Property Status": "Immobilien-Status", "Pool Size": "Pool-Größe", "Additional Rooms": "Zusätzliche Räume", "Last remodel year": "Letztes Renovierungsjahr", "Listed By": "<PERSON><PERSON><PERSON><PERSON> von", "View Photos": "Fotos anzeigen", "Similar Properties": "Ähnliche Immobilien", "Hide\r\n                                    Filter": "<PERSON><PERSON>\r\n                                    au<PERSON>", "List Property": "<PERSON><PERSON><PERSON><PERSON><PERSON> auf<PERSON>en", ":count properties found": ":count <PERSON><PERSON><PERSON><PERSON><PERSON> gefunden", ":count property found": ":count <PERSON><PERSON><PERSON><PERSON><PERSON> gefunden", "Name [a->z]": "Name [a->z]", "Name [z->a]": "Name [z->a]", "Sq:": "Qm:", "Search by keyword": "Nach Stichwort suchen", "All Type": "Alle Typen", "We couldn't find any properties.": "Wir konnten keine Immobilien finden.", "No Booking": "<PERSON><PERSON>", "phone": "Telefon", "email": "E-Mail", "Property Image": "Immobilien-Bild", "Add property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search Properties": "Immobilien suchen", "Listing Title": "Angebots-Titel", "Date published": "Veröffentlichungsdatum", "Property by :name": "<PERSON><PERSON><PERSON><PERSON><PERSON> von :name", "Featured Properties": "Hervorgehobene Immobilien", "Recently Viewed": "<PERSON><PERSON><PERSON><PERSON> angesehen", "Availability Properties": "Immobilien-Verfügbarkeit", "Layout 1": "Layout 1", "Layout 2": "Layout 2", "- Style Slider: List Item(s)": "- <PERSON><PERSON>lider: Listen-Element(e)", "Select property": "Immobilie auswählen", "Hide Slider Controls": "Slider-Steuerelemente ausblenden", "Banner Property": "Banner-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Show Attribute": "Attribut anzeigen", "Style 2 - Slider Carousel": "Stil 2 - <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "Style 6": "Stil 6", "Style 7": "Stil 7", "- Style: Background Image Uploader": "- Stil: Hintergrundbild-Uploader", "Video URL": "Video-URL", "Gallery Images": "Galerie-Bilder", "Video Url (Youtube, Vimeo, ..)": "Video-URL (YouTube, Vimeo, ..)", "Icon Class": "Icon-Klasse", "Image Text With Counting": "Bildtext mit Zählung", "List Location by ID": "Standort nach ID auflisten", "Property Map by location": "Immobilien-Karte nach Standort", "Style 2 : With Background Image": "Stil 2: <PERSON><PERSON>bil<PERSON>", "Background Image Style 4": "Hintergrundbild Stil 4", "Custom Title": "Benutzerdefinierter Titel", "Background Image": "Hintergrundbild", "Page Banner": "Seiten-Banner", ", Baths:": ", Bäder:", ", Sq:": ", Qm:", "Learn More": "<PERSON><PERSON> er<PERSON>", "Enter keyword ...": "Stichwort eingeben ...", "Choose Price": "<PERSON><PERSON> w<PERSON>hlen", "Hide": "Ausblenden", "Rent": "<PERSON><PERSON>", "You have just done the become agent request, please wait for the Admin\\'s approved": "Sie haben gerade die Agent-<PERSON><PERSON><PERSON> geste<PERSON>, bitte warten Sie auf die Genehmigung des Administrators", "Manage Contacts": "Kontakte verwalten", "--Select Filter--": "--<PERSON><PERSON>--", "Property contact": "Immobilien-Kontakt", "Agent contact": "Agent-<PERSON><PERSON><PERSON>", "Agency contact": "Agentur-Kontakt", "Object": "Objekt", "Guest name": "Gast-Name", "Created at": "Erstellt am", "') }}\r\n                                            @endif\r\n\r\n                                            @if ($row->object_model == 'property": "') }}\r\n                                            @endif\r\n\r\n                                            @if ($row->object_model == 'property", "Property views": "Immobilien-Aufrufe", "Become a agent": "Agent werden", "User Social": "<PERSON><PERSON>er-Social", "Scroll Down ID": "Nach unten scrollen ID", "List Layout": "Listen-Layout", "Grid Layout": "<PERSON><PERSON>-Layout", "About": "<PERSON><PERSON>", "Sale off :number": "Rabatt :number", "Starting from": "Ab", "per adult": "pro Erwachsener", "View Detail": "Details anzeigen", "Clear All": "Alle löschen", "Lorem ipsum dolor sit amet, consectetur.": "Lorem ipsum dolor sit amet, consectetur.", "Sign in": "Anmelden", "to book with your saved details or": "um mit Ihren gespeicherten Daten zu buchen oder", "register": "registrieren", "to manage your bookings on the go!": "um Ihre Buchungen unterwegs zu verwalten!", "Let us know who you are": "<PERSON><PERSON> un<PERSON> wissen, wer <PERSON> sind", "List Car by IDs": "Auto nach IDs auflisten", "Map Background in Button Show Map": "Karten-Hintergrund in Button Karte anzeigen", "Property highlights": "Immobilien-Highlights", "Select Number": "<PERSON><PERSON><PERSON> au<PERSON>w<PERSON>hl<PERSON>", "See All": "Alle anzeigen", "Show on map": "<PERSON><PERSON> anzeigen", "Send a message": "Eine Nachricht senden", "Full Name": "Vollständiger Name", "Your Messages": "<PERSON><PERSON><PERSON>", "Send a Messsage": "Eine Nachricht senden", "List Contact": "Kontakt auflisten", "Info Contact": "Info-Kontakt", "Iframe google map": "Iframe Google-Karte", "Why Choose Us": "Warum uns wählen", "Block: Title": "Block: Titel", "Block: Desc": "Block: Beschreibung", "List Items": "Elemente auflisten", "Title/Desc": "Titel/Beschreibung", "Footer Style": "Footer-Stil", "Style 8": "Stil 8", "Footer content left": "Footer-Inhalt links", "Footer content right": "Footer-Inhalt rechts", "Logo Preload": "Logo Preload", "Do you have a promo code?": "Haben Sie einen Promo-Code?", "Enter promo code": "Promo-Code eingeben", "Select term event": "Event-Be<PERSON>riff auswählen", "Event: Term Feature Box": "Event: Begriff-Feature-Box", "Term Icon": "Begriff-Icon", "Event snapshot": "Event-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ":number% of travelers recommend this experience": ":number% der Reisenden empfehlen diese Erfahrung", "Start Time: :time - ": "Startzeit: :time - ", "Destinations": "Re<PERSON><PERSON><PERSON>", "Price Filter": "Preisfilter", "Service Flight": "Service Flug", "Showing :from - :to of :total flights": "Zeige :from - :to von :total Flügen", "- List Item(s)": "- Listen-Element(e)", "Discover Title": "Entdecken-Titel", "Discover Link": "Entdecken-Link", "Normal 2": "Normal 2", "Slider Carousel V2": "Slider-Karussell V2", "Showing :from - :to of :total hotels": "Zeige :from - :to von :total Hotels", "Scroll Now": "Jetzt scrollen", "Select Room": "<PERSON><PERSON> au<PERSON>wählen", "Room Type": "Zimmertyp", "Benefits": "<PERSON><PERSON><PERSON><PERSON>", "Select Rooms": "<PERSON><PERSON> au<PERSON>wählen", "Show Room Information": "Zimmer-Informationen anzeigen", "adults": "Erwachsene", "children": "Kinder", "Hotel Rules - Policies": "Hotel-Regeln - Richtlinien", "See Availability": "Verfügbarkeit anzeigen", "Welcome back": "Willkommen zurück", "Sign In": "Anmelden", "or sign in with": "oder anmelden mit", "By creating an account, you agree to our Terms of Service and Privacy Statement.": "Durch die Erstellung eines Kontos stimmen Sie unseren Nutzungsbedingungen und Datenschutzerklärung zu.", "Sign in or create an account": "Anmelden oder Konto erstellen", "FAQs about": "FAQs über", "See All :count Photos": "Alle :count <PERSON><PERSON><PERSON> anzeigen", "Guest reviews": "Gäste-Bewertungen", "Leave a Reply": "Eine Antwort hinterlassen", "Your email address will not be published.": "Ihre E-Mail-Adresse wird nicht veröffentlicht.", "Write Your Comment": "<PERSON>hren Kommentar schreiben", "Post Comment": "Kommentar posten", "You must <a href='#login' data-bs-toggle='modal' data-target='#login'>log in</a> to write review": "<PERSON><PERSON> mü<PERSON> sich <a href='#login' data-bs-toggle='modal' data-target='#login'>an<PERSON><PERSON></a>, um eine Bewertung zu schreiben", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                    </span>\r\n                @endforeach\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"searchMenu-guests__field select-seat-type-dropdown shadow-2\" data-x-dd=\"searchMenu-guests\" data-x-dd-toggle=\"-is-active\">\r\n        <div class=\"bg-white px-30 py-30 rounded-4\">\r\n            @foreach($seatType as $type)\r\n                <?php\r\n                $inputName = 'seat_type_'.$type->code;\r\n                $inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n                ?>\r\n\r\n                <div class=\"row y-gap-10 justify-between items-center\">\r\n                    <div class=\"col-auto\">\r\n                        <div class=\"text-15 fw-500\">{{__('Adults :type": ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                    </span>\r\n                @endforeach\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"searchMenu-guests__field select-seat-type-dropdown shadow-2\" data-x-dd=\"searchMenu-guests\" data-x-dd-toggle=\"-is-active\">\r\n        <div class=\"bg-white px-30 py-30 rounded-4\">\r\n            @foreach($seatType as $type)\r\n                <?php\r\n                $inputName = 'seat_type_'.$type->code;\r\n                $inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n                ?>\r\n\r\n                <div class=\"row y-gap-10 justify-between items-center\">\r\n                    <div class=\"col-auto\">\r\n                        <div class=\"text-15 fw-500\">{{__('Erwachsene :type", "Where you'll be": "Wo <PERSON>e sein werden", "Your Travel Journey Starts Here": "Ihre Reise beginnt hier", "Sign up and we'll send the best deals to you": "Registrieren Si<PERSON> sich und wir senden Ihnen die besten Angebote", "Main Menu": "Hauptmenü", "Become An Expert": "Experte werden", "Sign In / Register": "Anmelden / Registrieren", "MENU": "MENÜ", "Style 9": "Stil 9", "Style 10": "Stil 10", "View All Url": "Alle URL anzeigen", "General info": "Allgemeine Informationen", "Desc:": "Beschreibung:", "View All Destinations": "Alle Reiseziele anzeigen", "Discover": "Entdecken", "travellers": "Reisende", "Explore deals, travel guides and things to do in :text": "<PERSON>t<PERSON><PERSON><PERSON>, Re<PERSON><PERSON>ührer und Aktivitäten in :text", "Interdum et malesuada fames ac ante ipsum": "Interdum et malesuada fames ac ante ipsum", "What to know before visiting :text": "Was <PERSON> vor dem Besuch von :text wissen sollten", "Most Popular :name": "Beliebteste :name", "Top sights in  :text": "Top-Sehenswürdigkeiten in :text", "These popular destinations have a lot to offer": "Diese beliebten Reiseziele haben viel zu bieten", "See More": "<PERSON><PERSON> <PERSON><PERSON>", "Header Align": "Header-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Left": "Links", "Center": "<PERSON><PERSON>", "Right": "<PERSON><PERSON><PERSON>", "Related content": "Verwandte Inhalte", "Interdum et malesuada fames": "Interdum et malesuada fames", "Normal White": "Normal Weiß", "Transparent V2": "Transparent V2", "Transparent V3": "Transparent V3", "Transparent V4": "Transparent V4", "Transparent V5": "Transparent V5", "Transparent V6": "Transparent V6", "Transparent V7": "Transparent V7", "Transparent V8": "Transparent V8", "Transparent V9": "Transparent V9", "Disable subscribe default": "Standard-Abonnement deaktivieren", "Bathroom": "<PERSON><PERSON><PERSON>", "nights": "<PERSON><PERSON>cht<PERSON>", "guests": "<PERSON><PERSON><PERSON>", ":count :guest": ":count :guest", ":count bathroom": ":count <PERSON><PERSON>", ":count bed": ":count <PERSON><PERSON>", "per night": "pro Nacht", "About Text": "Über Text", "Subtitle": "Untertitel", "Link Download": "Download-Link", "Download App": "App herunterladen", "Normal Ver 2": "Normal Ver 2", "Slider Ver 2": "Slider Ver 2", "List All Service": "Alle Services auflisten", "Login Register": "Anmelden Registrieren", "OverLay": "Overlay", "Subscribe Style": "Abonnement-Stil", "List Terms": "Begriffe auflisten", "Text Featured Box": "Text-Feature-Box", "Text Image": "Text-Bild", "Image 1": "Bild 1", "Image 2": "Bild 2", "Style 4 (Only List Item)": "Stil 4 (Nur Listen-Element)", "Style 5 (Only List Item)": "Stil 5 (Nur Listen-Element)", "Youtube Image": "YouTube-Bild", "Youtube Link": "YouTube-Link", "Job": "Job", "Happy people number": "<PERSON><PERSON>hl glücklicher Menschen", "Happy people text": "Text glücklicher Menschen", "Overall rating number": "Gesamtbewertungs-Nummer", "Overall rating text": "Gesamtbewertungs-Text", "Overall rating star": "Gesamtbewertungs-Stern", "Title Trusted": "Titel vertrauenswürdig", "List Trusted(s)": "Vertrauenswürdige auflisten", "Logo Image": "Logo-Bild", "Testimonial Background (For Style 4, Style 6, Style 7)": "Testimonial-<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>il 4, <PERSON><PERSON> 6, <PERSON><PERSON> 7)", "Book Title": "Buch-Titel", "Book Desc": "Buch-Beschreibung", "Book Url": "Buch-URL", "Book Url Text": "Buch-URL-Text", "Book Image": "Buch-Bild", "Tour: Tour Deals": "Tour: Tour-Angebote", "Tour: Tour Types": "Tour: Tour-Typen", "Category Icon Class": "Kategorie-Icon-Klasse", "Category icon": "Kategorie-Icon", "Watch Video": "<PERSON> ansehen", ":count Tours": ":count <PERSON><PERSON>", "Tour snapshot": "Tour-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Important information": "Wichtige Informationen", "Not sure? You can cancel this reservation up to 24 hours in advance for a full refund.": "Nicht sicher? Sie können diese Reservierung bis zu 24 Stunden im Voraus für eine vollständige Rückerstattung stornieren.", "See less": "<PERSON><PERSON> anzeigen", "See details & photo": "Details & Foto anzeigen", "Ready to jump back in?": "<PERSON><PERSON><PERSON>, wieder einzusteigen?", "Earning Statistics": "Verdienststatistiken", "No booking": "<PERSON><PERSON>", "Your avatar": "Ihr Ava<PERSON>", "PNG or JPG no bigger than 800px wide and tall.": "PNG oder JPG nicht größer als 800px breit und hoch.", "User Name": "<PERSON><PERSON><PERSON><PERSON>", "Page become an expert": "Seite Experte werden", "Website": "Website", "Founded Time": "Gründungszeit", ":from - :to of :total+ :agent available": ":from - :to von :total+ :agent verf<PERSON><PERSON><PERSON>", "Company Agent at ": "Firmen-Agent bei ", "Show less": "<PERSON><PERSON> anzeigen", "For Sale": "<PERSON><PERSON> ve<PERSON>en", "Professional Information": "Professionelle Informationen", "Agency address": "<PERSON>ur<PERSON><PERSON><PERSON><PERSON>", "Contact Form": "Kontaktformular", "There are many variations of passages.": "Es gibt viele Variationen von Passagen.", "Enter agent name": "Agent-<PERSON> e<PERSON><PERSON>", "Name (\r\n                                     a -> z )": "Name (\r\n                                     a -> z )", "Name (\r\n                                     z -> a )": "Name (\r\n                                     z -> a )", ":from - :to of :total+ :property available": ":from - :to von :total+ :property verfügbar", ":from - :to of :total+ :agency available": ":from - :to von :total+ :agency verfügbar", "Agency Information": "Agentur-<PERSON>en", "Broker address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Websites": "Websites", "Member since": "<PERSON><PERSON><PERSON><PERSON> seit", "View Listings": "Angebote anzeigen", "All Agencies": "Alle Agenturen", "Enter agency name": "Agentur-<PERSON> e<PERSON>ben", "All Cities": "Alle Städte", "Name (a -> z)": "Name (a -> z)", "Name (z -> a)": "Name (z -> a)", "Link Map": "<PERSON><PERSON><PERSON><PERSON>", "Form Title": "Formular-Titel", "Form Sub Title": "Formular-Untertitel", "Textarea": "Textbereich", "Footer Content": "Footer-Inhalt", "Phone Contact": "Telefon-Kontakt", "Currency:": "Währung:", "OR": "ODER", "Continue Google": "Mit Google fortfahren", "Continue Facebook": "Mit Facebook fortfahren", "Continue Apple": "<PERSON>t <PERSON> fortfahren", "Not signed up? ": "Noch nicht registriert? ", "Create an account.": "Ein Konto erstellen.", "Email Address": "E-Mail-Adresse", "Password Confirm": "Passwort bestätigen", "Create account": "<PERSON><PERSON> er<PERSON>", "Done": "<PERSON><PERSON><PERSON>", "Grid": "<PERSON><PERSON>", "List": "Liste", "Keep Yourself Up to Date": "Bleiben Sie auf dem Laufenden", "Login / Register": "Anmelden / Registrieren", "MANAGE LISTINGS": "ANGEBOTE VERWALTEN", "Add New Property": "Neue Immobilie hinzufügen", "My Properties": "<PERSON><PERSON>", "MANAGE ACCOUNT": "KONTO VERWALTEN", "Welcome to": "Will<PERSON>mmen bei", "New Account": "<PERSON><PERSON><PERSON>", "Number Items": "<PERSON><PERSON><PERSON>", "Button Title": "Button-Titel", "Button Link": "Button-Link", "Location Blocks": "Standort-Blöcke", "See All Properties": "Alle Immobilien anzeigen", "View City": "Stadt anzeigen", "See All Cities": "Alle Städte anzeigen", "Share this post": "<PERSON>sen Beitrag teilen", "Previous Post": "<PERSON><PERSON><PERSON><PERSON>", "Next Post": "Nächster Beitrag", ":from - :to of :total+ properties available": ":from - :to von :total+ Immobilien verfügbar", ":from - :to of :total+ news available": ":from - :to von :total+ News verfügbar", "We are glad to see you again!": "Wir freuen uns, <PERSON><PERSON> wied<PERSON>!", "Banner Image 1": "Banner-Bild 1", "Banner Image 2": "Banner-Bild 2", "Select Attributes": "Attribute auswählen", "Category Title": "Kategorie-Titel", "Category Limit": "Kategorie-Limit", "Select Categories": "Kate<PERSON><PERSON> auswählen", "Link Video Ember": "Link Video Ember", "Select Properties": "Immobilien auswählen", "List Counter": "Listen-<PERSON><PERSON><PERSON>", "Plus": "Plus", "Class Wrapper": "Klassen-Wrapper", "Form Search": "Formular-<PERSON><PERSON>", "Background Color": "Hintergrundfarbe", "List Categories": "<PERSON><PERSON><PERSON> auflisten", "Image Upload": "Bild-Upload", "List Featured Properties": "Hervorgehobene Immobilien auflisten", "Filter by category": "<PERSON><PERSON> Kategorie filtern", "Show Type": "<PERSON><PERSON>igen", "Both": "<PERSON><PERSON>", "If left blank the button will be hidden": "<PERSON><PERSON> leer gelassen, wird der Button ausgeblendet", "Custom Class (optional)": "Benutzerdefinierte Klasse (optional)", "Background": "Hi<PERSON>grund", "List Properties": "Immobilien auflisten", "Bedroom": "Schlafzimmer", "Bath": "Bad", "Sqft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Floor Plans": "<PERSON><PERSON><PERSON><PERSON>", "Bed": "<PERSON><PERSON>", "Sidebar": "Seitenleiste", "Map Search Layout": "<PERSON><PERSON>-Such-Layout", "Grid Cols": "Raster-Spalten", "Grid Style": "Raster-Stil", "Search desc": "Such-Beschreibung", "Search Background Image": "Such-Hintergrundbild", "Sidebar detail page": "Seitenleiste Detailseite", "Vendor information": "Anbieter-Informationen", "Vendor contact": "Anbieter-Kontakt", "Property search": "Immobilien-Suche", "Property featured": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "More Filter": "<PERSON><PERSON><PERSON>", "Price Range": "Preisspanne", "any": "beliebig", "Reset all filters": "Alle Filter zurücksetzen", "Enter a property name or an address": "Immobilien-Name oder Adresse e<PERSON>ben", "Enter Keyword": "Stichwort eingeben", "Any Category": "Beliebige Kategorie", "Popular Searches": "Beliebte Suchen", "Search products…": "Produkte suchen…", "sq ft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "View Details": "Details anzeigen", "Enter Keywords": "Stichwörter eingeben", "Looking For": "<PERSON><PERSON> nach", "Any Location": "Beliebiger Standort", "View detail": "Details anzeigen", "See All Categories": "Alle Kategorien anzeigen", "FOR SALE": "ZU VERKAUFEN", ":count bath": ":count Bad", "Total Free Customer Care": "Kostenloser Kundendienst gesamt", "Nee Live Support?": "Live-Support ben<PERSON><PERSON><PERSON>?", "bed": "<PERSON><PERSON>", "bath": "Bad", "See All :total Photos": "Alle :total Fotos anzeigen", "Property Description": "Immobilien-Beschreibung", "Size:": "Größe:", "Property Showcase": "Immobilien-Showcase", "Related Properties": "Verwandte Immobilien", "Request Information": "<PERSON><PERSON>", "Contact vendor": "Anbieter kontaktieren", "Enter Your Messages": "<PERSON>hre Nachrichten e<PERSON>ben", "Get More Information": "Weitere Informationen erhalten", "Contact Agent": "Agent <PERSON><PERSON><PERSON><PERSON>", ":number bed": ":number Bett", ":number bath": ":number Bad", "Features & Amenities": "Ausstattung & Annehmlichkeiten", "Find your home": "<PERSON><PERSON> finden", "All\r\n                        Type": "Alle\r\n                        Typen", "any bath": "beliebiges Bad", "any bed": "beliebiges Bett", "Any": "Beliebig", "Is Sold": "Ist verkauft", "Listing title": "Angebots-Titel", "Date Published": "Veröffentlichungsdatum", "Deletel": "Löschen", "Sign in with this account across the following sites.": "<PERSON>t diesem Konto auf den folgenden Seiten anmelden.", "Go Back To Homepages": "Zurück zur Startseite", "Image Uploader 2": "Bild-Uploader 2", "Image Uploader 3": "Bild-Uploader 3", "Icon Class - get class in <a href=\"https://www.flaticon.com\" target=\"_blank\">https://www.flaticon.com</a>": "Icon-Klasse - Klasse abrufen unter <a href=\"https://www.flaticon.com\" target=\"_blank\">https://www.flaticon.com</a>", "Title for list": "Titel für Liste", "First item is main item": "Erstes Element ist Hauptelement", "Link to": "<PERSON>", "Block Plans": "Block-Pläne", "Saving": "<PERSON><PERSON>", "List Plan(s)": "Plan(e) auflisten", "Filter by Plan": "<PERSON><PERSON>n", "Block Teams": "Block-Teams", "List Team(s)": "Team(s) auflisten", "Link": "Link", "Brands List": "Marken-Liste", "Custom Class": "Benutzerdefinierte Klasse", "List Brand(s)": "Marke(n) auflisten", "Brand Logo": "Marken-Logo", "Url": "URL", "Class (css)": "<PERSON><PERSON><PERSON> (CSS)", "Button Name 2": "Button-Name 2", "Button Url 2": "Button-URL 2", "Margin": "Rand", "Counter": "<PERSON><PERSON><PERSON>", "Button Name": "Button-Name", "Button Url": "Button-URL", "Block Download App": "Block App herunterladen", "List App(s) Download": "App(s) Download auflisten", "Link download": "Download-Link", "Fun Fact": "Interessante <PERSON>", "Animate": "<PERSON><PERSON><PERSON><PERSON>", "Icon Box": "Icon-Box", "Is Featured?": "Ist hervorgehoben?", "Text color": "Textfarbe", "List Fact Item(s)": "Fakten-Element(e) auflisten", "Why Choose us": "Warum uns wählen", "Featured Text": "Hervorgehobener Text", "Featured Value": "Hervorgehobener Wert", "List Text Item(s)": "Text-Element(e) auflisten", "Featured icon": "Hervorgehobenes Icon", "Billed Monthly": "<PERSON><PERSON><PERSON> a<PERSON>", "Billed Yearly": "Jä<PERSON>lich abgerechnet", "per month": "pro <PERSON><PERSON>", "Join": "Beitreten", "Password Confirm is required field": "Passwort-Bestätigung ist ein Pflichtfeld", "Pricing Icon": "Preis-Icon", "Filter: ": "Filter: ", "Manage Account": "<PERSON><PERSON> ver<PERSON>ten", "Membership Plans": "Mitgliedschaftspläne", "Save up to 20%": "Sparen Sie bis zu 20%", "Upload Profile Files": "Profil-<PERSON><PERSON>", "Photos must be JPEG or PNG format and least 2048x768": "Fotos müssen im JPEG- oder PNG-Format und mindestens 2048x768 sein", "About me": "<PERSON>ber mich", "Social Information": "Social-Informationen", "--Select Icon--": "--<PERSON><PERSON>--", "Update Profile": "Profil aktualisieren", "My Favorites": "<PERSON><PERSON>", "No Wishlist": "<PERSON><PERSON>", "Your trip": "<PERSON><PERSON><PERSON>", "About this boat": "<PERSON><PERSON> dies<PERSON>", "Boat's Location": "Boot-Standort", "Frequently asked questions": "Häufig gestellte Fragen", "/ per hour": "/ pro Stunde", "/ per day": "/ pro Tag", "Explore other options": "Andere Optionen erkunden", "Add to wishlist": "Zur Wunschliste hinzufügen", "Check": "Prüfen", "(:number Reviews)": "(:number Bewertungen)", "(:number Review)": "(:number Bewertung)", "Layout Detail": "Layout-Detail", "Seats": "Sitzplätze", "Gear": "Ausrüstung", "About this car": "<PERSON><PERSON> dieses Auto", "Car's Location": "Auto-Standort", "List Item(s) Contact": "Kontakt-Element(e) auflisten", "Error 404 background image": "Fehler 404 Hintergrundbild", "About this event": "<PERSON><PERSON> dieses Event", "Event Location": "Event-Standort", "All photo": "Alle Fotos", "View in a map": "<PERSON><PERSON> anzeigen", "About this hotel": "<PERSON>ber dieses Hotel", "Reserve a room": "Ein Zimmer reservieren", "Owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enter Email": "E-Mail eingeben", "Forgot Password": "Passwort vergessen", "Create an account": "Ein Konto erstellen", "Enter First Name": "<PERSON><PERSON><PERSON> e<PERSON>ben", "Enter Last Name": "Nachname e<PERSON>ben", "Enter Phone": "Telefon eingeben", "Enter Password": "Passwort eingeben", "I confirm that I have read and accepted the privacy policy": "<PERSON>ch bestätige, dass ich die Datenschutzrichtlinie gelesen und akzeptiert habe", "Already have an account": "<PERSON>ben Si<PERSON> bereits ein Konto", "Information Contact": "Informations-Kontakt", "Show on the list": "In der Liste anzeigen", "Sign in to your account": "In Ihr Konto anmelden", "Block subscribe settings": "Block-Abonnement-Einstellungen", "Settings for block subscribe": "Einstellungen für Block-Abonnement", "Subscribe title": "Abonnement-Titel", "Subscribe sub title": "Abonnement-Untertitel", "Subscribe Image": "Abonnement-Bild", "Stories, tips, and guides": "Geschichten, Tipps und Leitfäden", "Post navigation": "Beitrags-Navigation", "BY": "VON", "404 Page": "404-Seite", "Go to home": "Zur Startsei<PERSON>", "View Less": "<PERSON><PERSON> anzeigen", "Leave a review": "Eine Bewertung hinterlassen", "Required fields are marked": "Pflichtfelder sind markiert", "About this rental": "Über diese Vermietung", "Rental's Location": "Vermietungs-Standort", "List About": "Über auflisten", "Other Blocks": "<PERSON><PERSON> Blöcke", "List Item(s) Right": "Element(e) rechts auflisten", "Google Map Block": "Google Map Block", "Iframe Google Map": "Iframe Google Map", "Icon Image": "Icon-Bild", "Icon Class - get class in <a href=\"https://fontawesome.com/v4/icons/\" target=\"_blank\">https://fontawesome.com/v4/icons/</a>": "Icon-Klasse - Klasse abrufen unter <a href=\"https://fontawesome.com/v4/icons/\" target=\"_blank\">https://fontawesome.com/v4/icons/</a>", "Number Star": "<PERSON><PERSON><PERSON>", "Video Block": "Video-Block", "Video Link": "Video-Link", "About this tour": "<PERSON><PERSON> diese Tour", "Payment": "Zahlung", "Fee:": "Gebühr:", "Includes": "Inbegriffen", "Excludes": "Ausgeschlossen", "Filter Search": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Other Settings": "Andere Einstellungen", "Why Book With Us?": "Warum bei uns buchen?", "Class icon": "Klassen-Icon", "Customer care available 24/7": "Kundendienst 24/7 verfügbar", "Title - Link info": "Titel - Link-Info", "By continuing, you agree to the": "Durch Fortfahren stimmen Si<PERSON> den", "Terms and Conditions": "Allgemeinen Geschäftsbedingungen zu", "CONFIRM BOOKING": "BUCHUNG BESTÄTIGEN", "Your Card Information": "<PERSON><PERSON>e Ka<PERSON>in<PERSON>en", "Thank You. Your booking was submitted successfully!": "Vielen Dank. Ihre Buchung wurde erfolgreich übermittelt!", "Your booking status is: :status": "Ihr Buchungsstatus ist: :status", "Car Blocks": "Auto-Blöcke", "Car: List Term Items": "Auto: Begriff-Elemente auflisten", "Specifications": "Spezifikationen", "Specifications List": "Spezifikations-Liste", "Specifications Desc": "Spezifikations-Beschreibung", "Specifications name": "Spezifikations-Name", "SAVE :text": ":text SPAREN", "Pick Up Date": "Abholdatum", "Save for later": "<PERSON><PERSON><PERSON> später speichern", "View On Map": "<PERSON><PERSON> anzeigen", "Sends us a Message": "Senden Si<PERSON> uns e<PERSON> Nachricht", "Link View on Map": "<PERSON> anzeigen", "Footer Info Contact": "Footer-Info-Kontakt", "Logo Color": "Logo-Farbe", "Logo Text": "Logo-Text", "List Event by IDs": "Event nach IDs auflisten", "Event Blocks": "Event-<PERSON><PERSON><PERSON><PERSON>", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                        </span>\r\n\t\t\t\t\t@endforeach\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dropdown-menu custom-select-dropdown\">\r\n\t\t\t@foreach($seatType as $type)\r\n\t\t\t\t<?php\r\n                $inputName = 'seat_type_'.$type->code;\r\n                $inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n                ;?>\r\n\t\t\t\r\n\t\t\t\t<div class=\"dropdown-item-row\">\r\n\t\t\t\t\t<div class=\"label\">{{__('Adults :type": ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                        </span>\r\n\t\t\t\t\t@endforeach\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dropdown-menu custom-select-dropdown\">\r\n\t\t\t@foreach($seatType as $type)\r\n\t\t\t\t<?php\r\n                $inputName = 'seat_type_'.$type->code;\r\n                $inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n                ;?>\r\n\t\t\t\r\n\t\t\t\t<div class=\"dropdown-item-row\">\r\n\t\t\t\t\t<div class=\"label\">{{__('Erwachsene :type", "Show all": "Alle anzeigen", "Flight Details": "Flug-Details", "List Hotel by IDs": "Hotel nach IDs auflisten", "Hotel Blocks": "Hotel-Blöcke", "Badge tag": "Badge-Tag", "Eg: service VIP": "Z.B.: Service VIP", "Brown": "<PERSON>", "Maroon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Green": "<PERSON><PERSON><PERSON><PERSON>", "Danger": "<PERSON><PERSON><PERSON><PERSON>", "Info": "Info", "Dark": "<PERSON><PERSON><PERSON>", "Eg: Service VIP": "Z.B.: Service VIP", "night": "<PERSON><PERSON>", "Address-Description": "Adresse-Beschreibung", "Select Your Room": "<PERSON><PERSON> Z<PERSON> au<PERSON>wählen", "Room": "<PERSON><PERSON>", "Mailing List": "Mailing-Liste", "Sign up for our mailing list to get latest updates and offers.": "Melden Sie sich für unsere Mailing-Liste an, um die neuesten Updates und Angebote zu erhalten.", "Page navigation": "Seiten-Navigation", "Sign in or Register": "Anmelden oder Registrieren", "First row 2 cards": "Erste Reihe 2 Karten", "Style 3 cards/ row": "Stil 3 Karten/Reihe", "First row 3 cards": "Erste Reihe 3 Karten", "Slide 4 cards/slider": "Slide 4 Karten/Slider", "Style 5 cards/ row": "Stil 5 Karten/Reihe", "Location Name": "Standort-Name", "Location Desc": "Standort-Beschreibung", "Location Button Text": "Standort-Button-Text", "Location Button Link": "Standort-Button-Link", "Number Item (Default: 4)": "<PERSON><PERSON><PERSON> (Standard: 4)", "Unmissable Destinations": "Unverzichtbare Reiseziele", "Welcome to :name": "Will<PERSON>mmen in :name", "Top Experiences in :name": "Top-Erlebnisse in :name", "Recent articles": "Aktuelle Artikel", "Read More Articles": "<PERSON><PERSON><PERSON> Artikel lesen", "Company or title": "Unternehmen oder Titel", "List Space by IDs": "Raum nach IDs auflisten", "Space Blocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ":num :text": ":num :text", "List Brand Item(s)": "Marken-Element(e) auflisten", "Breadcrumb Section": "Breadcrumb-Bereich", "Background Gradient overlay": "Hintergrund-Gradient-Overlay", "Grayish Blue": "Gräulich Blau", "Blue Light": "Hellblau", "Orange": "Orange", "Tab button Pills": "Tab-<PERSON><PERSON>", "Tab button Boxed": "Tab-<PERSON>ton Boxed", "Tab button Shadow": "Tab-<PERSON><PERSON>", "- Style 1: Background Image Uploader": "- Stil 1: Hintergrundbild-Uploader", "single form search": "Einzelformular-Suche", "- Style 1 : Image Uploader": "- Stil 1: Bild-Uploader", "- Style 2, Style 3 : Icon Class": "- Stil 2, Stil 3: Icon-Klasse", "Video Caption": "Video-Untertitel", "List Tour by IDs": "Tour nach IDs auflisten", "Tour Blocks": "Tour-<PERSON><PERSON><PERSON><PERSON>", "Date From-To": "<PERSON><PERSON>", "Min age": "Mindestalter", "Pickup": "<PERSON><PERSON><PERSON><PERSON>", "Wifi available": "WLAN verfügbar", "Max People": "<PERSON><PERSON>", "Wifi Available": "WLAN verfügbar", "Min Age:": "Mindestalter:", "Pickup:": "<PERSON><PERSON><PERSON><PERSON>:", "$row->pickup": "$row->pickup", "Price Includes": "<PERSON><PERSON> be<PERSON>", "Price Excludes": "Preis ausgeschlossen", "auth.failed": "Diese Anmeldedaten stimmen nicht mit unseren Aufzeichnungen überein.", "The provided two factor authentication code was invalid.": "Der angegebene Zwei-Faktor-Authentifizierungscode war ungültig.", "The provided password was incorrect.": "Das angegebene Passwort war falsch.", "The provided two factor recovery code was invalid.": "Der angegebene Zwei-Faktor-Wiederherstellungscode war ungültig.", "auth.throttle": "<PERSON>u viele Anmeldeversuche. Bitte versuchen Sie es in :seconds <PERSON><PERSON>nden erneut.", "The :attribute must be at least :length characters and contain at least one uppercase character.": "Das :attribute muss mindestens :length Zeichen lang sein und mindestens einen Großbuchstaben enthalten.", "The :attribute must be at least :length characters and contain at least one number.": "Das :attribute muss mindestens :length Zeichen lang sein und mindestens eine Zahl enthalten.", "The :attribute must be at least :length characters and contain at least one special character.": "Das :attribute muss mindestens :length Zeichen lang sein und mindestens ein Sonderzeichen enthalten.", "The :attribute must be at least :length characters and contain at least one uppercase character and one number.": "Das :attribute muss mindestens :length Zeichen lang sein und mindestens einen Großbuchstaben und eine Zahl enthalten.", "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.": "Das :attribute muss mindestens :length Zeichen lang sein und mindestens einen Großbuchstaben und ein Sonderzeichen enthalten.", "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.": "Das :attribute muss mindestens :length Zeichen lang sein und mindestens einen Großbuchstaben, eine Zahl und ein Sonderzeichen enthalten.", "The :attribute must be at least :length characters and contain at least one special character and one number.": "Das :attribute muss mindestens :length Zeichen lang sein und mindestens ein Sonderzeichen und eine Zahl enthalten.", "The :attribute must be at least :length characters.": "Das :attribute muss mindestens :length <PERSON><PERSON><PERSON> lang sein.", "Reset Password Notification": "Passwort-Reset-Benachrichtigung", "You are receiving this email because we received a password reset request for your account.": "Sie erhalten diese E-Mail, weil wir eine Passwort-Reset-Anfrage für Ihr Konto erhalten haben.", "This password reset link will expire in :count minutes.": "Dieser Passwort-<PERSON>set-<PERSON> l<PERSON> in :count <PERSON><PERSON><PERSON> ab.", "If you did not request a password reset, no further action is required.": "<PERSON>n Sie keinen Passwort-<PERSON><PERSON> ange<PERSON> haben, ist keine weitere Aktion erforderlich.", "Please click the button below to verify your email address.": "Bitte klicken Sie auf die Schaltfläche unten, um Ihre E-Mail-Adresse zu verifizieren.", "If you did not create an account, no further action is required.": "<PERSON>n Si<PERSON> kein Konto erstellt haben, ist keine weitere Aktion erforderlich.", "Payment Required": "Zahlung er<PERSON>lich", "All rights reserved.": "Alle Rechte vorbehalten.", "Whoops!": "Ups!", "Hello!": "Hallo!", "to": "bis", "of": "von", "results": "Ergebnisse", "Pagination Navigation": "Paginierungs-Navigation", "Go to page :page": "<PERSON><PERSON><PERSON> zu Se<PERSON> :page", "There was an error on row :row. :message": "<PERSON><PERSON> gab e<PERSON> in Zeile :row. :message", "diff_now": "jetzt", "diff_yesterday": "g<PERSON>n", "diff_tomorrow": "morgen", "diff_before_yesterday": "vorgestern", "diff_after_tomorrow": "übermorgen", "period_recurrences": "Wiederholungen", "period_interval": "Intervall", "period_start_date": "Startdatum", "period_end_date": "Enddatum", "validation.phone": "Das :attribute-<PERSON>ld muss eine gültige Telefonnummer sein.", "installer_messages.final.finished": "Abgeschlossen", "installer_messages.environment.success": "Ihre .env-Datei-Einstellungen wurden gespeichert.", "installer_messages.environment.errors": "Die .env-<PERSON><PERSON> konnte nicht gespeichert werden. Bitte erstellen Si<PERSON> sie manuell.", "installer_messages.installed.success_log_message": "Laravel-Installer erfolgreich INSTALLIERT am ", "installer_messages.updater.log.success_message": "Laravel-Installer erfolgreich AKTUALISIERT am ", "installer_messages.environment.wizard.tabs.environment": "Umgebung", "installer_messages.environment.wizard.tabs.database": "Datenbank", "installer_messages.environment.wizard.tabs.application": "<PERSON><PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_name_label": "App-Name", "installer_messages.environment.wizard.form.app_url_label": "App-URL", "installer_messages.environment.wizard.form.buttons.setup_application": "<PERSON><PERSON><PERSON><PERSON> ein<PERSON>", "installer_messages.final.migration": "Migration & Seed Konsolen-Ausgabe:", "installer_messages.final.console": "Anwendungs-Konsolen-Ausgabe:", "installer_messages.final.env": "Finale .env-Datei:", "installer_messages.welcome.title": "<PERSON><PERSON>-Installer", "There are two apples": "<PERSON><PERSON> gibt zwei Äpfel"}