<?php

namespace Modules\Core\Admin;

use Illuminate\Http\Request;
use Mo<PERSON>les\AdminController;
use <PERSON><PERSON>les\ServiceProvider;

class ModuleController extends AdminController
{
    public function __construct()
    {
        $this->setActiveMenu(route('core.admin.tool.index'));
    }

    public function index(){
        $data = [
            'page_title'=>__("Module Management"),
            'rows'=>ServiceProvider::getManageableModules()
        ];

        return view('Core::admin.module.index',$data);
    }

    public function bulkEdit(Request $request)
    {
        if(is_demo_mode()){
            return redirect()->back()->with("error","DEMO MODE: You are not allowed to do it");
        }

        $ids = $request->input('ids');
        $action = $request->input('action');

        if (empty($ids)) {
            return redirect()->back()->with('error', __('Select at least 1 item!'));
        }

        if (empty($action)) {
            return redirect()->back()->with('error', __('Select an Action!'));
        }

        $manageableModules = ServiceProvider::getManageableModules();
        $successCount = 0;

        foreach ($ids as $moduleId) {
            if (!isset($manageableModules[$moduleId])) {
                continue; // Skip invalid module IDs
            }

            $settingKey = strtolower($moduleId) . '_disable';

            switch ($action) {
                case 'enable':
                    setting_update_item($settingKey, false);
                    $successCount++;
                    break;
                case 'disable':
                    setting_update_item($settingKey, true);
                    $successCount++;
                    break;
            }
        }

        if ($successCount > 0) {
            $actionText = $action == 'enable' ? __('enabled') : __('disabled');
            return redirect()->back()->with('success', __(':count modules have been :action successfully', [
                'count' => $successCount,
                'action' => $actionText
            ]));
        }

        return redirect()->back()->with('error', __('No modules were updated'));
    }

    public function toggleStatus(Request $request, $moduleId)
    {
        if(is_demo_mode()){
            return response()->json(['error' => 'DEMO MODE: You are not allowed to do it'], 403);
        }

        $manageableModules = ServiceProvider::getManageableModules();

        if (!isset($manageableModules[$moduleId])) {
            return response()->json(['error' => __('Module not found')], 404);
        }

        $settingKey = strtolower($moduleId) . '_disable';
        $currentStatus = setting_item($settingKey, false);
        $newStatus = !$currentStatus;

        setting_update_item($settingKey, $newStatus);

        return response()->json([
            'success' => true,
            'status' => !$newStatus, // Return enabled status (opposite of disable flag)
            'message' => $newStatus ? __('Module disabled successfully') : __('Module enabled successfully')
        ]);
    }
}
